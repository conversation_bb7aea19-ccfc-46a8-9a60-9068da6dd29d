"""
Browser management using zendriver for antidetect functionality
"""

import os
import json
import asyncio
from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger

# Import zendriver from local installation
import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "zendriver_local"))

try:
    import zendriver
    from zendriver import Browser
    logger.info("Successfully imported zendriver from local installation")
except ImportError as e:
    logger.error(f"Failed to import zendriver: {e}")
    # Fallback - try system installation
    try:
        import zendriver
        from zendriver import Browser
        logger.info("Using system zendriver installation")
    except ImportError:
        logger.error("Zendriver not available - browser functionality will be limited")
        zendriver = None
        Browser = None

class BrowserManager:
    """Manages browser instances with antidetect capabilities"""

    def __init__(self):
        self.browsers: Dict[str, Browser] = {}  # profile_id -> browser instance
        self.profiles_dir = Path("browser_sessions")
        self.profiles_dir.mkdir(exist_ok=True)
        
    async def create_browser_profile(self, profile_id: str, profile_config: Dict[str, Any]) -> bool:
        """Create a new browser profile with antidetect settings"""
        try:
            profile_path = self.profiles_dir / profile_id
            profile_path.mkdir(exist_ok=True)
            
            # Create browser configuration
            browser_config = {
                "user_agent": profile_config.get("user_agent", self._get_default_user_agent()),
                "screen_resolution": profile_config.get("screen_resolution", "1920x1080"),
                "timezone": profile_config.get("timezone", "America/New_York"),
                "language": profile_config.get("language", "en-US"),
                "proxy": self._format_proxy_config(profile_config),
                "profile_path": str(profile_path)
            }
            
            # Save configuration
            config_file = profile_path / "config.json"
            with open(config_file, 'w') as f:
                json.dump(browser_config, f, indent=2)
            
            logger.info(f"Created browser profile: {profile_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create browser profile {profile_id}: {e}")
            return False
    
    async def launch_browser(self, profile_id: str) -> Optional[Browser]:
        """Launch browser with specific profile"""
        try:
            if profile_id in self.browsers:
                logger.warning(f"Browser already running for profile {profile_id}")
                return self.browsers[profile_id]

            profile_path = self.profiles_dir / f"profile_{profile_id}"
            config_file = profile_path / "config.json"

            if not config_file.exists():
                logger.error(f"Profile configuration not found: {profile_id}")
                return None

            # Load configuration
            with open(config_file, 'r') as f:
                config = json.load(f)

            # Create zendriver browser options
            browser_options = {
                "user_data_dir": str(profile_path),
                "headless": os.getenv("BROWSER_HEADLESS", "false").lower() == "true",
                "sandbox": False,  # Disable sandbox for macOS compatibility
                "browser_executable_path": self._get_chrome_executable_path(),
                "browser_connection_timeout": 10.0,
                "browser_connection_max_tries": 5,
            }

            # Add proxy if configured
            if config.get("proxy"):
                proxy_config = config["proxy"]
                if proxy_config.get("server"):
                    browser_options["proxy_server"] = proxy_config["server"]
                    if proxy_config.get("username"):
                        browser_options["proxy_username"] = proxy_config["username"]
                        browser_options["proxy_password"] = proxy_config.get("password", "")

            # Launch browser with zendriver
            browser = await zendriver.start(**browser_options)

            # Set user agent if specified
            if config.get("user_agent"):
                try:
                    # Try to set user agent using zendriver's method
                    page = await browser.get_page()
                    await page.evaluate(f'navigator.__defineGetter__("userAgent", function(){{return "{config["user_agent"]}"}});')
                except Exception as e:
                    logger.warning(f"Could not set user agent: {e}")

            # Set timezone if specified
            if config.get("timezone"):
                try:
                    # Try to set timezone using zendriver's method
                    page = await browser.get_page()
                    await page.evaluate(f'Intl.DateTimeFormat = function() {{ return {{ resolvedOptions: () => ({{ timeZone: "{config["timezone"]}" }}) }} }};')
                except Exception as e:
                    logger.warning(f"Could not set timezone: {e}")

            # Set language if specified
            if config.get("language"):
                try:
                    # Try to set language using zendriver's method
                    page = await browser.get_page()
                    await page.evaluate(f'Object.defineProperty(navigator, "language", {{get: function() {{ return "{config["language"]}"; }} }});')
                except Exception as e:
                    logger.warning(f"Could not set language: {e}")

            self.browsers[profile_id] = browser
            logger.info(f"Browser launched successfully for profile: {profile_id}")

            return browser

        except Exception as e:
            logger.error(f"Failed to launch browser for profile {profile_id}: {e}")
            return None
    
    async def close_browser(self, profile_id: str) -> bool:
        """Close browser for specific profile"""
        try:
            if profile_id not in self.browsers:
                logger.warning(f"No browser running for profile {profile_id}")
                return True

            browser = self.browsers[profile_id]

            # Close browser with zendriver
            await browser.quit()

            del self.browsers[profile_id]
            logger.info(f"Browser closed for profile: {profile_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to close browser for profile {profile_id}: {e}")
            return False

    async def get_browser(self, profile_id: str) -> Optional[Browser]:
        """Get existing browser instance"""
        return self.browsers.get(profile_id)
    
    async def is_browser_running(self, profile_id: str) -> bool:
        """Check if browser is running for profile"""
        return profile_id in self.browsers
    
    async def close_all_browsers(self):
        """Close all running browsers"""
        for profile_id in list(self.browsers.keys()):
            await self.close_browser(profile_id)
    
    def _get_default_user_agent(self) -> str:
        """Get default user agent string"""
        return "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    
    def _format_proxy_config(self, profile_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Format proxy configuration"""
        if not profile_config.get("proxy_host"):
            return None
        
        proxy_config = {
            "server": f"{profile_config.get('proxy_type', 'http')}://{profile_config['proxy_host']}:{profile_config.get('proxy_port', 8080)}"
        }
        
        if profile_config.get("proxy_username"):
            proxy_config["username"] = profile_config["proxy_username"]
            proxy_config["password"] = profile_config.get("proxy_password", "")
        
        return proxy_config
    
    async def update_profile_config(self, profile_id: str, config_updates: Dict[str, Any]) -> bool:
        """Update browser profile configuration"""
        try:
            profile_path = self.profiles_dir / profile_id
            config_file = profile_path / "config.json"
            
            if not config_file.exists():
                logger.error(f"Profile configuration not found: {profile_id}")
                return False
            
            # Load existing configuration
            with open(config_file, 'r') as f:
                config = json.load(f)
            
            # Update configuration
            config.update(config_updates)
            
            # Save updated configuration
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"Updated profile configuration: {profile_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update profile configuration {profile_id}: {e}")
            return False
    
    async def delete_profile(self, profile_id: str) -> bool:
        """Delete browser profile"""
        try:
            # Close browser if running
            if profile_id in self.browsers:
                await self.close_browser(profile_id)
            
            # Delete profile directory
            profile_path = self.profiles_dir / profile_id
            if profile_path.exists():
                import shutil
                shutil.rmtree(profile_path)
            
            logger.info(f"Deleted browser profile: {profile_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete profile {profile_id}: {e}")
            return False

    def _get_chrome_executable_path(self) -> str:
        """Get Chrome executable path for different platforms."""
        import platform
        import os

        system = platform.system().lower()

        # Common Chrome paths for different platforms
        chrome_paths = []

        if system == "darwin":  # macOS
            chrome_paths = [
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/Applications/Chromium.app/Contents/MacOS/Chromium",
            ]
        elif system == "linux":
            chrome_paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/google-chrome-stable",
                "/usr/bin/chromium-browser",
                "/usr/bin/chromium",
                "/snap/bin/chromium",
            ]
        elif system == "windows":
            chrome_paths = [
                "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe",
                "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe".format(os.getenv("USERNAME", "")),
            ]

        # Find first existing path
        for path in chrome_paths:
            if os.path.exists(path):
                return path

        # If no path found, return None to let zendriver auto-detect
        return None

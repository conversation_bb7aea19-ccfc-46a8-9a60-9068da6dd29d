"""
Facebook Session Handler for managing login sessions and validation
"""

import asyncio
import json
import time
from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger
from urllib.parse import urlparse, parse_qs

# Import zendriver from local installation
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "zendriver_local"))

try:
    from zendriver import Browser
except ImportError:
    logger.warning("Zendriver not available - Facebook session handling will be limited")
    Browser = None
from .browser_manager import BrowserManager


class FacebookSessionHandler:
    """Handles Facebook login sessions and validation"""
    
    def __init__(self, browser_manager: BrowserManager):
        self.browser_manager = browser_manager
        self.sessions: Dict[str, Dict[str, Any]] = {}  # profile_id -> session_info
        
    async def initiate_login(self, profile_id: str) -> Dict[str, Any]:
        """Initiate Facebook login process"""
        try:
            # Launch browser for profile
            browser = await self.browser_manager.launch_browser(profile_id)
            if not browser:
                return {"success": False, "error": "Failed to launch browser"}
            
            # Navigate to Facebook login
            page = await browser.get("https://www.facebook.com/login")
            
            # Wait for page to load
            await asyncio.sleep(3)
            
            # Check if already logged in
            current_url = await page.evaluate("window.location.href")
            if "facebook.com/login" not in current_url and "facebook.com" in current_url:
                logger.info(f"Profile {profile_id} already logged in")
                return await self._validate_existing_session(profile_id, browser)
            
            # Store session info
            self.sessions[profile_id] = {
                "status": "login_initiated",
                "browser": browser,
                "start_time": time.time(),
                "login_url": current_url
            }
            
            logger.info(f"Facebook login initiated for profile: {profile_id}")
            return {
                "success": True,
                "status": "login_initiated",
                "message": "Please complete login manually in the browser window"
            }
            
        except Exception as e:
            logger.error(f"Failed to initiate login for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def check_login_status(self, profile_id: str) -> Dict[str, Any]:
        """Check current login status"""
        try:
            if profile_id not in self.sessions:
                return {"success": False, "error": "No login session found"}
            
            session = self.sessions[profile_id]
            browser = session.get("browser")
            
            if not browser:
                return {"success": False, "error": "Browser session not found"}
            
            # Get current page
            page = await browser.get_current_page()
            if not page:
                return {"success": False, "error": "No active page found"}
            
            # Check current URL
            current_url = await page.evaluate("window.location.href")
            
            # Check if on login page
            if "facebook.com/login" in current_url:
                return {
                    "success": True,
                    "status": "awaiting_login",
                    "message": "Still on login page, please complete login"
                }
            
            # Check if on Facebook main pages (logged in)
            facebook_domains = ["facebook.com", "m.facebook.com"]
            if any(domain in current_url for domain in facebook_domains):
                # Additional validation
                is_logged_in = await self._validate_login_state(page)
                if is_logged_in:
                    session["status"] = "logged_in"
                    session["login_time"] = time.time()
                    return {
                        "success": True,
                        "status": "logged_in",
                        "message": "Successfully logged in to Facebook"
                    }
            
            return {
                "success": True,
                "status": "unknown",
                "current_url": current_url,
                "message": "Login status unclear, please check manually"
            }
            
        except Exception as e:
            logger.error(f"Failed to check login status for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def complete_login(self, profile_id: str) -> Dict[str, Any]:
        """Mark login as complete and validate session"""
        try:
            if profile_id not in self.sessions:
                return {"success": False, "error": "No login session found"}
            
            session = self.sessions[profile_id]
            browser = session.get("browser")
            
            if not browser:
                return {"success": False, "error": "Browser session not found"}
            
            # Validate login
            page = await browser.get_current_page()
            if not page:
                return {"success": False, "error": "No active page found"}
            
            is_logged_in = await self._validate_login_state(page)
            
            if is_logged_in:
                # Update session
                session["status"] = "logged_in"
                session["login_time"] = time.time()
                
                # Extract session cookies for future use
                cookies = await self._extract_session_cookies(browser)
                session["cookies"] = cookies
                
                logger.info(f"Login completed successfully for profile: {profile_id}")
                return {
                    "success": True,
                    "status": "logged_in",
                    "message": "Login completed successfully"
                }
            else:
                return {
                    "success": False,
                    "error": "Login validation failed, please ensure you are logged in"
                }
                
        except Exception as e:
            logger.error(f"Failed to complete login for profile {profile_id}: {e}")
            return {"success": False, "error": str(e)}
    
    async def _validate_existing_session(self, profile_id: str, browser: Browser) -> Dict[str, Any]:
        """Validate existing Facebook session"""
        try:
            page = await browser.get_current_page()
            if not page:
                return {"success": False, "error": "No active page found"}
            
            is_logged_in = await self._validate_login_state(page)
            
            if is_logged_in:
                self.sessions[profile_id] = {
                    "status": "logged_in",
                    "browser": browser,
                    "login_time": time.time()
                }
                
                return {
                    "success": True,
                    "status": "logged_in",
                    "message": "Already logged in to Facebook"
                }
            else:
                return {
                    "success": False,
                    "error": "Existing session is not valid"
                }
                
        except Exception as e:
            logger.error(f"Failed to validate existing session: {e}")
            return {"success": False, "error": str(e)}
    
    async def _validate_login_state(self, page) -> bool:
        """Validate if user is actually logged in to Facebook"""
        try:
            # Check for common logged-in indicators
            indicators = [
                "div[role='banner']",  # Facebook header
                "div[data-pagelet='LeftRail']",  # Left sidebar
                "div[aria-label='Account']",  # Account menu
                "a[aria-label='Profile']",  # Profile link
            ]
            
            for indicator in indicators:
                try:
                    element = await page.find_element("css selector", indicator)
                    if element:
                        return True
                except:
                    continue
            
            # Check if we can access user info
            try:
                user_info = await page.evaluate("""
                    () => {
                        const userLinks = document.querySelectorAll('a[href*="/profile.php"], a[href*="/"]');
                        for (let link of userLinks) {
                            if (link.href.includes('profile.php') || link.textContent.trim()) {
                                return true;
                            }
                        }
                        return false;
                    }
                """)
                return bool(user_info)
            except:
                pass
            
            return False
            
        except Exception as e:
            logger.error(f"Error validating login state: {e}")
            return False
    
    async def _extract_session_cookies(self, browser: Browser) -> List[Dict[str, Any]]:
        """Extract session cookies for future use"""
        try:
            cookies = await browser.get_cookies()
            return cookies
        except Exception as e:
            logger.error(f"Failed to extract cookies: {e}")
            return []
    
    async def get_session_info(self, profile_id: str) -> Optional[Dict[str, Any]]:
        """Get session information for profile"""
        session = self.sessions.get(profile_id)
        if session:
            # Remove browser object from returned info (not serializable)
            info = session.copy()
            if "browser" in info:
                del info["browser"]
            return info
        return None
    
    async def close_session(self, profile_id: str) -> bool:
        """Close Facebook session"""
        try:
            if profile_id in self.sessions:
                session = self.sessions[profile_id]
                browser = session.get("browser")
                
                if browser:
                    await self.browser_manager.close_browser(profile_id)
                
                del self.sessions[profile_id]
                logger.info(f"Session closed for profile: {profile_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to close session for profile {profile_id}: {e}")
            return False
    
    async def is_session_active(self, profile_id: str) -> bool:
        """Check if session is active"""
        return profile_id in self.sessions and self.sessions[profile_id].get("status") == "logged_in"
    
    async def get_active_sessions(self) -> List[str]:
        """Get list of active session profile IDs"""
        return [
            profile_id for profile_id, session in self.sessions.items()
            if session.get("status") == "logged_in"
        ]

"""
Facebook UID Extraction Logic with multiple pattern support
"""

import re
import asyncio
from typing import Set, List, Dict, Any, Optional
from urllib.parse import urlparse, parse_qs
from loguru import logger

# Import zendriver from local installation
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent / "zendriver_local"))

try:
    from zendriver import Browser
except ImportError:
    logger.warning("Zendriver not available - some functionality will be limited")
    Browser = None


class FacebookUIDExtractor:
    """Extracts Facebook UIDs from various sources with multiple pattern support"""
    
    def __init__(self):
        # Multiple UID extraction patterns
        self.uid_patterns = [
            # Standard profile URLs
            r'/profile\.php\?id=(\d+)',
            r'/user/(\d+)',
            r'/people/[^/]+/(\d+)',
            
            # Comment and post URLs
            r'/groups/\d+/user/(\d+)',
            r'/groups/[^/]+/user/(\d+)',
            r'profile_id=(\d+)',
            r'user_id=(\d+)',
            r'id=(\d+)',
            
            # Story and media URLs
            r'/story\.php\?story_fbid=\d+&id=(\d+)',
            r'/photo\.php.*&fbid=\d+.*&id=(\d+)',
            r'/video\.php.*&v=\d+.*&id=(\d+)',
            
            # Direct numeric patterns in URLs
            r'facebook\.com/(\d{8,})',
            r'fb\.com/(\d{8,})',
            
            # Data attributes and JSON patterns
            r'"profile_id":"(\d+)"',
            r'"user_id":"(\d+)"',
            r'"id":"(\d+)"',
            r'data-profileid="(\d+)"',
            r'data-userid="(\d+)"',
        ]
        
        # Compiled patterns for better performance
        self.compiled_patterns = [re.compile(pattern) for pattern in self.uid_patterns]
        
    def extract_uids_from_text(self, text: str) -> Set[str]:
        """Extract UIDs from text using multiple patterns"""
        uids = set()
        
        try:
            for pattern in self.compiled_patterns:
                matches = pattern.findall(text)
                for match in matches:
                    # Ensure it's a valid UID (numeric and reasonable length)
                    if match.isdigit() and 8 <= len(match) <= 20:
                        uids.add(match)
                        
        except Exception as e:
            logger.error(f"Error extracting UIDs from text: {e}")
            
        return uids
    
    def extract_uids_from_urls(self, urls: List[str]) -> Set[str]:
        """Extract UIDs from a list of URLs"""
        uids = set()
        
        for url in urls:
            try:
                # Extract from URL directly
                url_uids = self.extract_uids_from_text(url)
                uids.update(url_uids)
                
                # Parse URL parameters
                parsed = urlparse(url)
                params = parse_qs(parsed.query)
                
                # Check common parameter names
                param_names = ['id', 'user_id', 'profile_id', 'fbid']
                for param_name in param_names:
                    if param_name in params:
                        for value in params[param_name]:
                            if value.isdigit() and 8 <= len(value) <= 20:
                                uids.add(value)
                                
            except Exception as e:
                logger.error(f"Error extracting UID from URL {url}: {e}")
                
        return uids
    
    async def extract_uids_from_page(self, browser: Browser, page_url: str) -> Set[str]:
        """Extract UIDs from a Facebook page"""
        uids = set()
        
        try:
            # Navigate to page
            page = await browser.get(page_url)
            await asyncio.sleep(2)
            
            # Get page source
            page_source = await page.get_page_source()
            
            # Extract UIDs from page source
            uids.update(self.extract_uids_from_text(page_source))
            
            # Extract from all links on page
            links = await self._extract_all_links(page)
            uids.update(self.extract_uids_from_urls(links))
            
            # Extract from data attributes
            data_uids = await self._extract_from_data_attributes(page)
            uids.update(data_uids)
            
            logger.info(f"Extracted {len(uids)} UIDs from page: {page_url}")
            
        except Exception as e:
            logger.error(f"Error extracting UIDs from page {page_url}: {e}")
            
        return uids
    
    async def _extract_all_links(self, page) -> List[str]:
        """Extract all links from page"""
        try:
            links = await page.evaluate("""
                () => {
                    const links = Array.from(document.querySelectorAll('a[href]'));
                    return links.map(link => link.href).filter(href => href.includes('facebook.com') || href.includes('fb.com'));
                }
            """)
            return links or []
        except Exception as e:
            logger.error(f"Error extracting links: {e}")
            return []
    
    async def _extract_from_data_attributes(self, page) -> Set[str]:
        """Extract UIDs from data attributes"""
        uids = set()
        
        try:
            # Extract from common data attributes
            data_attributes = await page.evaluate("""
                () => {
                    const elements = document.querySelectorAll('[data-profileid], [data-userid], [data-id]');
                    const data = [];
                    elements.forEach(el => {
                        if (el.dataset.profileid) data.push(el.dataset.profileid);
                        if (el.dataset.userid) data.push(el.dataset.userid);
                        if (el.dataset.id) data.push(el.dataset.id);
                    });
                    return data;
                }
            """)
            
            for uid in data_attributes or []:
                if uid and uid.isdigit() and 8 <= len(uid) <= 20:
                    uids.add(uid)
                    
        except Exception as e:
            logger.error(f"Error extracting from data attributes: {e}")
            
        return uids
    
    def validate_uid(self, uid: str) -> bool:
        """Validate if a UID is valid"""
        try:
            # Must be numeric
            if not uid.isdigit():
                return False
                
            # Must be reasonable length (Facebook UIDs are typically 8-20 digits)
            if not (8 <= len(uid) <= 20):
                return False
                
            # Must not be all zeros or ones
            if uid == '0' * len(uid) or uid == '1' * len(uid):
                return False
                
            return True
            
        except Exception:
            return False
    
    def filter_valid_uids(self, uids: Set[str]) -> Set[str]:
        """Filter and return only valid UIDs"""
        return {uid for uid in uids if self.validate_uid(uid)}
    
    def deduplicate_uids(self, uid_list: List[str]) -> List[str]:
        """Remove duplicate UIDs while preserving order"""
        seen = set()
        result = []
        
        for uid in uid_list:
            if uid not in seen and self.validate_uid(uid):
                seen.add(uid)
                result.append(uid)
                
        return result
    
    async def extract_uids_from_comments_section(self, page) -> Set[str]:
        """Extract UIDs specifically from comments section"""
        uids = set()
        
        try:
            # Look for comment-specific selectors
            comment_selectors = [
                'div[data-testid="UFI2Comment/root"]',
                'div[role="article"]',
                'div[data-testid="comment"]',
                'li[data-testid="comment"]',
                '.UFIComment',
                '.UFICommentContent',
            ]
            
            for selector in comment_selectors:
                try:
                    # Extract UIDs from comment elements
                    comment_uids = await page.evaluate(f"""
                        (selector) => {{
                            const comments = document.querySelectorAll(selector);
                            const uids = [];
                            
                            comments.forEach(comment => {{
                                // Extract from links
                                const links = comment.querySelectorAll('a[href]');
                                links.forEach(link => {{
                                    const href = link.href;
                                    if (href.includes('facebook.com') || href.includes('fb.com')) {{
                                        uids.push(href);
                                    }}
                                }});
                                
                                // Extract from data attributes
                                if (comment.dataset.profileid) uids.push(comment.dataset.profileid);
                                if (comment.dataset.userid) uids.push(comment.dataset.userid);
                                
                                // Extract from inner HTML
                                uids.push(comment.innerHTML);
                            }});
                            
                            return uids;
                        }}
                    """, selector)
                    
                    # Process extracted data
                    for item in comment_uids or []:
                        if isinstance(item, str):
                            extracted = self.extract_uids_from_text(item)
                            uids.update(extracted)
                            
                except Exception as e:
                    logger.debug(f"Error with selector {selector}: {e}")
                    continue
                    
        except Exception as e:
            logger.error(f"Error extracting UIDs from comments: {e}")
            
        return uids
    
    def get_extraction_stats(self, uids: Set[str]) -> Dict[str, Any]:
        """Get statistics about extracted UIDs"""
        return {
            "total_uids": len(uids),
            "valid_uids": len(self.filter_valid_uids(uids)),
            "uid_length_distribution": {
                str(length): len([uid for uid in uids if len(uid) == length])
                for length in range(8, 21)
            }
        }

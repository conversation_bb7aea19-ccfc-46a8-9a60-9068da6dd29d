2025-07-07 10:20:11 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751858411444-4446660688: GET /api/scraping/sessions
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751858411444-4446660688: 200
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751858413491-4446700240: GET /api/scraping/sessions
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751858413491-4446700240: 200
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858415539-4446704272: GET /api/scraping/sessions
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858415539-4446704272: 200
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858417587-4446709456: GET /api/scraping/sessions
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858417587-4446709456: 200
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858419631-4446714576: GET /api/scraping/sessions
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858419631-4446714576: 200
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858421679-4446659856: GET /api/scraping/sessions
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858421679-4446659856: 200
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858423727-4446662352: GET /api/scraping/sessions
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858423727-4446662352: 200
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858425773-4446706768: GET /api/scraping/sessions
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858425773-4446706768: 200
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858427817-4446665168: GET /api/scraping/sessions
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858427817-4446665168: 200
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858429862-4446706576: GET /api/scraping/sessions
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858429862-4446706576: 200
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858431897-4446705232: GET /api/scraping/sessions
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858431897-4446705232: 200
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751858433936-4446664592: GET /api/scraping/sessions
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751858433936-4446664592: 200
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751858435982-4446445840: GET /api/scraping/sessions
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751858435982-4446445840: 200
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858438025-4446713296: GET /api/scraping/sessions
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858438025-4446713296: 200
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858440072-4446701392: GET /api/scraping/sessions
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858440072-4446701392: 200
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858442112-4446658448: GET /api/scraping/sessions
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858442112-4446658448: 200
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751858444157-4445832144: GET /api/scraping/sessions
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751858444157-4445832144: 200
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751858446194-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751858446194-4446713552: 200
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751858448231-4446666512: GET /api/scraping/sessions
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751858448231-4446666512: 200
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858450271-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858450271-4446713552: 200
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751858452315-4446704656: GET /api/scraping/sessions
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751858452315-4446704656: 200
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751858454364-4446700368: GET /api/scraping/sessions
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751858454364-4446700368: 200
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751858456410-4446658768: GET /api/scraping/sessions
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751858456410-4446658768: 200
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751858458455-4446705552: GET /api/scraping/sessions
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751858458455-4446705552: 200
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751858460498-4446715024: GET /api/scraping/sessions
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751858460498-4446715024: 200
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858462540-4446664592: GET /api/scraping/sessions
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858462540-4446664592: 200
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751858464588-4446630032: GET /api/scraping/sessions
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751858464588-4446630032: 200
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751858466634-4446699920: GET /api/scraping/sessions
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751858466634-4446699920: 200
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751858468680-4446701776: GET /api/scraping/sessions
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751858468680-4446701776: 200
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751858470728-4446663184: GET /api/scraping/sessions
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751858470728-4446663184: 200
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751858472777-4446665680: GET /api/scraping/sessions
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751858472777-4446665680: 200
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751858474825-4446703504: GET /api/scraping/sessions
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751858474825-4446703504: 200
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751858476875-4446708752: GET /api/scraping/sessions
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751858476875-4446708752: 200
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751858478922-4446705424: GET /api/scraping/sessions
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751858478922-4446705424: 200
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751858480961-4446712720: GET /api/scraping/sessions
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751858480961-4446712720: 200
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858483006-4446651408: GET /api/scraping/sessions
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858483006-4446651408: 200
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858485053-4446554256: GET /api/scraping/sessions
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858485053-4446554256: 200
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858487100-4446703376: GET /api/scraping/sessions
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858487100-4446703376: 200
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858489143-4446709840: GET /api/scraping/sessions
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858489143-4446709840: 200
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858491184-4446662416: GET /api/scraping/sessions
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858491184-4446662416: 200
2025-07-07 10:22:14 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:22:14 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:22:14 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:22:14 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535407-4461760784: GET /api/scraping/sessions
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535407-4461760784: 200
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535546-4462398800: GET /api/profiles
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535546-4462398800: 307
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535557-4462399248: GET /api/profiles/
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535557-4462399248: 200
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858537455-4462469008: GET /api/scraping/sessions
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858537455-4462469008: 200
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858539494-4462475152: GET /api/scraping/sessions
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858539494-4462475152: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541268-4462406992: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541269-4462405968: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541271-4462608912: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541277-4461759248: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541271-4462608912: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541277-4461759248: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541288-4462660048: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541290-4462668560: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541269-4462405968: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541268-4462406992: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541290-4462668560: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541288-4462660048: 200
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858543370-4462668944: GET /api/scraping/sessions
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858543370-4462668944: 200
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858545419-4473227856: GET /api/scraping/sessions
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858545419-4473227856: 200
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858547467-4462620048: GET /api/scraping/sessions
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858547467-4462620048: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549655-4462397264: OPTIONS /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549655-4462397264: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549661-4462471504: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549670-4462609488: DELETE /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549661-4462471504: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549670-4462609488: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549714-4462462480: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549714-4462462480: 200
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858551746-4473300496: GET /api/scraping/sessions
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858551746-4473300496: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554233-4462472464: OPTIONS /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554233-4462472464: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554237-4462623760: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554247-4473301264: DELETE /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554237-4462623760: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554247-4473301264: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554290-4473279504: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554290-4473279504: 200
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751858556317-4473284688: GET /api/scraping/sessions
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751858556317-4473284688: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558623-4473279184: OPTIONS /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558623-4473279184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558628-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558638-4473277136: DELETE /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558628-4462671184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558638-4473277136: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558696-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558696-4462671184: 200
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560636-4473243792: POST /api/scraping/sessions/24/start
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560636-4473243792: 200
2025-07-07 10:22:40 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 10:22:40 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 10:22:40 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560678-4462665040: GET /api/scraping/sessions
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560678-4462665040: 200
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858562699-4462619472: GET /api/scraping/sessions
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858562699-4462619472: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563579-4473226000: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563581-4473250704: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563583-4462622928: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563585-4473249104: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563583-4462622928: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563585-4473249104: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563579-4473226000: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563581-4473250704: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563629-4462474448: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563635-4473285200: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563629-4462474448: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563635-4473285200: 200
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751858565679-4462466576: GET /api/scraping/sessions
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751858565679-4462466576: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567757-4473410576: GET /api/analytics/dashboard
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567762-4473297040: GET /api/campaigns
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567762-4473297040: 307
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567774-4473254736: GET /api/campaigns/
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567774-4473254736: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567757-4473410576: 200
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570224-4473405968: GET /api/profiles
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570224-4473405968: 307
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570246-4473417936: GET /api/profiles/
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570246-4473417936: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582795-4462471568: GET /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-status
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582795-4462471568: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582832-4473300496: OPTIONS /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582832-4473300496: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582870-4473426448: POST /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582870-4473426448: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582901-4473432272: GET /api/profiles
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582901-4473432272: 307
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582932-4473472720: GET /api/profiles/
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582932-4473472720: 200
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858597332-4473479824: POST /api/profiles/16cc6849-c8fe-4eaf-b833-d90f8e8ebfce/facebook-login
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858597332-4473479824: 200
2025-07-07 10:27:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:28 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:28 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:28 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:28 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:27:55 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:55 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:55 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:56 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:56 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:56 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:56 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:28:51 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:28:51 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:28:51 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:28:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:28:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:28:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:28:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:30 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:30 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:30 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:31 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:42 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:42 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:42 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:43 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:43 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:43 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:43 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:31:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:31:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:31:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859090159-4646364944: POST /api/profiles/
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859090159-4646364944: 500
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751859156308-4465319440: POST /api/profiles/
2025-07-07 10:32:36 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859156308-4465319440: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337695), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337698), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751859185074-4466590352: POST /api/profiles/
2025-07-07 10:33:05 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859185074-4466590352: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78261), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78264), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:33:35 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859278069-4430405712: POST /api/profiles/
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751859278069-4430405712: 400
2025-07-07 10:35:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751859304473-4431088784: POST /api/profiles/
2025-07-07 10:35:04 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859304473-4431088784: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478992), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478996), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:35:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751859341457-4432731344: POST /api/profiles/
2025-07-07 10:35:41 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859341457-4432731344: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462525), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462531), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859386580-4436366992: POST /api/profiles/
2025-07-07 10:36:26 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859386580-4436366992: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609720), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609724), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751859409997-4437472528: GET /api/profiles/
2025-07-07 10:36:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751859409997-4437472528: 200
2025-07-07 10:36:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751859417887-4437677904: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:36:58 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:58 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:00 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:00 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:00 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:00 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:00 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751859417887-4437677904: 200
2025-07-07 10:37:02 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:02 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:02 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:03 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:03 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:03 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:03 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859511763-4714570192: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:38:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751859511763-4714570192: 200
2025-07-07 10:38:33 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:33 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751859543456-4509066064: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751859543456-4509066064: 200
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751859549449-4509741328: GET /api/profiles/browser-sessions
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751859549449-4509741328: 500
2025-07-07 10:39:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859566828-4509741840: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:39:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:27 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:27 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:27 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751859566828-4509741840: 200
2025-07-07 10:39:28 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:28 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:28 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:29 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:29 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:29 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:29 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859570903-4606598288: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859570903-4606598288: 200
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751859611980-4607212304: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751859611980-4607212304: 200
2025-07-07 10:42:56 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:56 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:56 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:57 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:57 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:57 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:42:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751859825615-4436698640: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:43:45 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:45 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:45 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751859825615-4436698640: 200
2025-07-07 10:43:47 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:47 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:47 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:48 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:48 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:48 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:48 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859878991-4596082128: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:44:39 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:39 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:39 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:40 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:40 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:40 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:40 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751859878991-4596082128: 200
2025-07-07 10:44:41 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:41 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:41 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:42 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:42 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:42 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:42 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751859884077-4648463248: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751859884077-4648463248: 200
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751859922507-4649156240: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751859922507-4649156240: 200
2025-07-07 10:46:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:46:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:46:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:46:24 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:46:24 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:46:24 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:46:24 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991185-4722943568: GET /api/profiles
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991185-4722943568: 307
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991249-4723053776: GET /api/profiles/
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991249-4723053776: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003841-4723668560: OPTIONS /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003841-4723668560: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003853-4723713296: DELETE /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003853-4723713296: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003875-4723711184: GET /api/profiles
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003875-4723711184: 307
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003901-4722943376: GET /api/profiles/
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003901-4722943376: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008638-4723711504: OPTIONS /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008638-4723711504: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008651-4722946896: DELETE /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008651-4722946896: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008669-4723779984: GET /api/profiles
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008669-4723779984: 307
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008692-4723776080: GET /api/profiles/
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008692-4723776080: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023257-4723802192: OPTIONS /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023257-4723802192: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023284-4723804752: POST /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023284-4723804752: 307
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023321-4724887632: OPTIONS /api/profiles/
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023321-4724887632: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023328-4723781584: POST /api/profiles/
2025-07-07 10:47:03 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751860023328-4723781584: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338822), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338827), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:47:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:47:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:47:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:48:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:48:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:48:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:48:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751860146155-4381579920: POST /api/profiles/
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751860146155-4381579920: 201
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751860173297-4382803328: GET /api/profiles/
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751860173297-4382803328: 200
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860182173-4381684112: POST /api/profiles/
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860182173-4381684112: 201
2025-07-07 10:51:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751860279560-4382881776: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/facebook-login
2025-07-07 10:51:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751860279560-4382881776: 200
2025-07-07 10:51:21 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:51:21 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:51:21 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:51:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:51:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:51:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:51:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860300272-4639696304: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860300272-4639696304: 500
2025-07-07 10:52:23 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:23 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:50 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:50 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:50 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:50 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:50 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:01 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:01 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:11 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:11 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:11 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:12 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:12 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:12 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:12 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:38 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:38 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:38 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:39 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:39 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:39 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:39 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:52 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:52 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:05 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:54:05 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:54:05 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:54:06 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:54:06 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:54:06 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:54:06 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751860457109-4442616640: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751860457109-4442616640: 200
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751860464091-4443400704: PUT /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751860464091-4443400704: 200
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860482448-4443475104: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/test-proxy
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860482448-4443475104: 200
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751860487572-4443399456: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/zendriver-config
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751860487572-4443399456: 200
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860503080-4395241536: POST /api/profiles/
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860503080-4395241536: 201
2025-07-07 10:57:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648831-4417764368: GET /api/profiles
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648831-4417764368: 307
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648883-4417858128: GET /api/profiles/
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648883-4417858128: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660480-4418502352: OPTIONS /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660480-4418502352: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660491-4418547280: DELETE /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660491-4418547280: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660513-4418493328: GET /api/profiles
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660513-4418493328: 307
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660540-4417849168: GET /api/profiles/
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660540-4417849168: 200
2025-07-07 10:57:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751860672560-4418588496: POST /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad/facebook-login
2025-07-07 10:57:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751860672560-4418588496: 200
2025-07-07 10:57:54 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:57:54 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:57:54 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:57:55 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:55 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:55 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:55 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:09:58 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398402-4659033104: GET /api/profiles
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398402-4659033104: 307
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398466-4659110288: GET /api/profiles/
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398466-4659110288: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408421-4659762128: OPTIONS /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408421-4659762128: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408433-4659667664: PUT /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408433-4659667664: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408468-4659808080: GET /api/profiles
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408468-4659808080: 307
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408510-4659766416: GET /api/profiles/
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408510-4659766416: 200
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411624-4659819408: GET /api/profiles
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411624-4659819408: 307
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411640-4659761616: GET /api/profiles/
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411640-4659761616: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425348-4659846672: OPTIONS /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425348-4659846672: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425365-4659851024: POST /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425365-4659851024: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425376-4660959568: OPTIONS /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425376-4660959568: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425386-4659852816: POST /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425386-4659852816: 201
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425406-4660998672: GET /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425406-4660998672: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425458-4661090960: GET /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425458-4661090960: 200
2025-07-07 11:10:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861428903-4660967312: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login
2025-07-07 11:10:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861428903-4660967312: 200
2025-07-07 11:10:31 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:10:31 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:10:31 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:10:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:10:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:10:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:10:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000

2025-07-07 10:20:11 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751858411444-4446660688: GET /api/scraping/sessions
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751858411444-4446660688: 200
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751858413491-4446700240: GET /api/scraping/sessions
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751858413491-4446700240: 200
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858415539-4446704272: GET /api/scraping/sessions
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858415539-4446704272: 200
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858417587-4446709456: GET /api/scraping/sessions
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858417587-4446709456: 200
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858419631-4446714576: GET /api/scraping/sessions
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858419631-4446714576: 200
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858421679-4446659856: GET /api/scraping/sessions
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858421679-4446659856: 200
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858423727-4446662352: GET /api/scraping/sessions
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858423727-4446662352: 200
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858425773-4446706768: GET /api/scraping/sessions
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858425773-4446706768: 200
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858427817-4446665168: GET /api/scraping/sessions
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858427817-4446665168: 200
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858429862-4446706576: GET /api/scraping/sessions
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858429862-4446706576: 200
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858431897-4446705232: GET /api/scraping/sessions
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858431897-4446705232: 200
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751858433936-4446664592: GET /api/scraping/sessions
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751858433936-4446664592: 200
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751858435982-4446445840: GET /api/scraping/sessions
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751858435982-4446445840: 200
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858438025-4446713296: GET /api/scraping/sessions
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858438025-4446713296: 200
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858440072-4446701392: GET /api/scraping/sessions
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858440072-4446701392: 200
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858442112-4446658448: GET /api/scraping/sessions
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858442112-4446658448: 200
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751858444157-4445832144: GET /api/scraping/sessions
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751858444157-4445832144: 200
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751858446194-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751858446194-4446713552: 200
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751858448231-4446666512: GET /api/scraping/sessions
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751858448231-4446666512: 200
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858450271-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858450271-4446713552: 200
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751858452315-4446704656: GET /api/scraping/sessions
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751858452315-4446704656: 200
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751858454364-4446700368: GET /api/scraping/sessions
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751858454364-4446700368: 200
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751858456410-4446658768: GET /api/scraping/sessions
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751858456410-4446658768: 200
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751858458455-4446705552: GET /api/scraping/sessions
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751858458455-4446705552: 200
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751858460498-4446715024: GET /api/scraping/sessions
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751858460498-4446715024: 200
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858462540-4446664592: GET /api/scraping/sessions
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858462540-4446664592: 200
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751858464588-4446630032: GET /api/scraping/sessions
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751858464588-4446630032: 200
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751858466634-4446699920: GET /api/scraping/sessions
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751858466634-4446699920: 200
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751858468680-4446701776: GET /api/scraping/sessions
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751858468680-4446701776: 200
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751858470728-4446663184: GET /api/scraping/sessions
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751858470728-4446663184: 200
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751858472777-4446665680: GET /api/scraping/sessions
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751858472777-4446665680: 200
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751858474825-4446703504: GET /api/scraping/sessions
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751858474825-4446703504: 200
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751858476875-4446708752: GET /api/scraping/sessions
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751858476875-4446708752: 200
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751858478922-4446705424: GET /api/scraping/sessions
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751858478922-4446705424: 200
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751858480961-4446712720: GET /api/scraping/sessions
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751858480961-4446712720: 200
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858483006-4446651408: GET /api/scraping/sessions
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858483006-4446651408: 200
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858485053-4446554256: GET /api/scraping/sessions
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858485053-4446554256: 200
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858487100-4446703376: GET /api/scraping/sessions
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858487100-4446703376: 200
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858489143-4446709840: GET /api/scraping/sessions
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858489143-4446709840: 200
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858491184-4446662416: GET /api/scraping/sessions
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858491184-4446662416: 200
2025-07-07 10:22:14 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:22:14 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:22:14 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:22:14 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535407-4461760784: GET /api/scraping/sessions
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535407-4461760784: 200
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535546-4462398800: GET /api/profiles
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535546-4462398800: 307
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535557-4462399248: GET /api/profiles/
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535557-4462399248: 200
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858537455-4462469008: GET /api/scraping/sessions
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858537455-4462469008: 200
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858539494-4462475152: GET /api/scraping/sessions
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858539494-4462475152: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541268-4462406992: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541269-4462405968: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541271-4462608912: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541277-4461759248: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541271-4462608912: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541277-4461759248: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541288-4462660048: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541290-4462668560: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541269-4462405968: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541268-4462406992: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541290-4462668560: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541288-4462660048: 200
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858543370-4462668944: GET /api/scraping/sessions
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858543370-4462668944: 200
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858545419-4473227856: GET /api/scraping/sessions
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858545419-4473227856: 200
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858547467-4462620048: GET /api/scraping/sessions
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858547467-4462620048: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549655-4462397264: OPTIONS /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549655-4462397264: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549661-4462471504: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549670-4462609488: DELETE /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549661-4462471504: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549670-4462609488: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549714-4462462480: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549714-4462462480: 200
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858551746-4473300496: GET /api/scraping/sessions
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858551746-4473300496: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554233-4462472464: OPTIONS /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554233-4462472464: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554237-4462623760: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554247-4473301264: DELETE /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554237-4462623760: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554247-4473301264: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554290-4473279504: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554290-4473279504: 200
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751858556317-4473284688: GET /api/scraping/sessions
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751858556317-4473284688: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558623-4473279184: OPTIONS /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558623-4473279184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558628-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558638-4473277136: DELETE /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558628-4462671184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558638-4473277136: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558696-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558696-4462671184: 200
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560636-4473243792: POST /api/scraping/sessions/24/start
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560636-4473243792: 200
2025-07-07 10:22:40 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 10:22:40 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 10:22:40 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560678-4462665040: GET /api/scraping/sessions
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560678-4462665040: 200
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858562699-4462619472: GET /api/scraping/sessions
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858562699-4462619472: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563579-4473226000: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563581-4473250704: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563583-4462622928: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563585-4473249104: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563583-4462622928: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563585-4473249104: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563579-4473226000: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563581-4473250704: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563629-4462474448: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563635-4473285200: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563629-4462474448: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563635-4473285200: 200
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751858565679-4462466576: GET /api/scraping/sessions
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751858565679-4462466576: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567757-4473410576: GET /api/analytics/dashboard
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567762-4473297040: GET /api/campaigns
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567762-4473297040: 307
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567774-4473254736: GET /api/campaigns/
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567774-4473254736: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567757-4473410576: 200
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570224-4473405968: GET /api/profiles
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570224-4473405968: 307
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570246-4473417936: GET /api/profiles/
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570246-4473417936: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582795-4462471568: GET /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-status
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582795-4462471568: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582832-4473300496: OPTIONS /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582832-4473300496: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582870-4473426448: POST /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582870-4473426448: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582901-4473432272: GET /api/profiles
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582901-4473432272: 307
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582932-4473472720: GET /api/profiles/
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582932-4473472720: 200
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858597332-4473479824: POST /api/profiles/16cc6849-c8fe-4eaf-b833-d90f8e8ebfce/facebook-login
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858597332-4473479824: 200
2025-07-07 10:27:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:28 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:28 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:28 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:28 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:27:55 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:55 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:55 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:56 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:56 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:56 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:56 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:28:51 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:28:51 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:28:51 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:28:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:28:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:28:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:28:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:30 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:30 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:30 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:31 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:42 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:42 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:42 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:43 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:43 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:43 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:43 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:31:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:31:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:31:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859090159-4646364944: POST /api/profiles/
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859090159-4646364944: 500
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751859156308-4465319440: POST /api/profiles/
2025-07-07 10:32:36 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859156308-4465319440: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337695), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337698), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751859185074-4466590352: POST /api/profiles/
2025-07-07 10:33:05 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859185074-4466590352: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78261), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78264), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:33:35 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859278069-4430405712: POST /api/profiles/
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751859278069-4430405712: 400
2025-07-07 10:35:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751859304473-4431088784: POST /api/profiles/
2025-07-07 10:35:04 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859304473-4431088784: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478992), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478996), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:35:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751859341457-4432731344: POST /api/profiles/
2025-07-07 10:35:41 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859341457-4432731344: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462525), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462531), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859386580-4436366992: POST /api/profiles/
2025-07-07 10:36:26 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859386580-4436366992: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609720), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609724), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751859409997-4437472528: GET /api/profiles/
2025-07-07 10:36:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751859409997-4437472528: 200
2025-07-07 10:36:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751859417887-4437677904: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:36:58 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:58 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:00 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:00 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:00 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:00 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:00 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751859417887-4437677904: 200
2025-07-07 10:37:02 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:02 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:02 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:03 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:03 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:03 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:03 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859511763-4714570192: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:38:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751859511763-4714570192: 200
2025-07-07 10:38:33 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:33 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751859543456-4509066064: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751859543456-4509066064: 200
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751859549449-4509741328: GET /api/profiles/browser-sessions
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751859549449-4509741328: 500
2025-07-07 10:39:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859566828-4509741840: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:39:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:27 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:27 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:27 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751859566828-4509741840: 200
2025-07-07 10:39:28 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:28 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:28 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:29 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:29 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:29 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:29 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859570903-4606598288: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859570903-4606598288: 200
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751859611980-4607212304: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751859611980-4607212304: 200
2025-07-07 10:42:56 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:56 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:56 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:57 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:57 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:57 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:42:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751859825615-4436698640: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:43:45 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:45 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:45 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751859825615-4436698640: 200
2025-07-07 10:43:47 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:47 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:47 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:48 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:48 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:48 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:48 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859878991-4596082128: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:44:39 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:39 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:39 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:40 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:40 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:40 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:40 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751859878991-4596082128: 200
2025-07-07 10:44:41 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:41 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:41 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:42 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:42 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:42 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:42 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751859884077-4648463248: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751859884077-4648463248: 200
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751859922507-4649156240: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751859922507-4649156240: 200
2025-07-07 10:46:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:46:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:46:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:46:24 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:46:24 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:46:24 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:46:24 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991185-4722943568: GET /api/profiles
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991185-4722943568: 307
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991249-4723053776: GET /api/profiles/
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991249-4723053776: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003841-4723668560: OPTIONS /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003841-4723668560: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003853-4723713296: DELETE /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003853-4723713296: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003875-4723711184: GET /api/profiles
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003875-4723711184: 307
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003901-4722943376: GET /api/profiles/
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003901-4722943376: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008638-4723711504: OPTIONS /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008638-4723711504: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008651-4722946896: DELETE /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008651-4722946896: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008669-4723779984: GET /api/profiles
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008669-4723779984: 307
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008692-4723776080: GET /api/profiles/
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008692-4723776080: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023257-4723802192: OPTIONS /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023257-4723802192: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023284-4723804752: POST /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023284-4723804752: 307
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023321-4724887632: OPTIONS /api/profiles/
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023321-4724887632: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023328-4723781584: POST /api/profiles/
2025-07-07 10:47:03 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751860023328-4723781584: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338822), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338827), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:47:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:47:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:47:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:48:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:48:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:48:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:48:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751860146155-4381579920: POST /api/profiles/
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751860146155-4381579920: 201
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751860173297-4382803328: GET /api/profiles/
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751860173297-4382803328: 200
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860182173-4381684112: POST /api/profiles/
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860182173-4381684112: 201
2025-07-07 10:51:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751860279560-4382881776: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/facebook-login
2025-07-07 10:51:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751860279560-4382881776: 200
2025-07-07 10:51:21 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:51:21 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:51:21 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:51:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:51:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:51:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:51:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860300272-4639696304: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860300272-4639696304: 500
2025-07-07 10:52:23 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:23 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:50 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:50 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:50 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:50 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:50 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:01 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:01 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:11 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:11 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:11 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:12 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:12 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:12 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:12 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:38 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:38 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:38 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:39 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:39 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:39 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:39 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:52 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:52 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:05 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:54:05 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:54:05 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:54:06 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:54:06 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:54:06 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:54:06 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751860457109-4442616640: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751860457109-4442616640: 200
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751860464091-4443400704: PUT /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751860464091-4443400704: 200
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860482448-4443475104: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/test-proxy
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860482448-4443475104: 200
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751860487572-4443399456: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/zendriver-config
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751860487572-4443399456: 200
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860503080-4395241536: POST /api/profiles/
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860503080-4395241536: 201
2025-07-07 10:57:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648831-4417764368: GET /api/profiles
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648831-4417764368: 307
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648883-4417858128: GET /api/profiles/
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648883-4417858128: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660480-4418502352: OPTIONS /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660480-4418502352: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660491-4418547280: DELETE /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660491-4418547280: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660513-4418493328: GET /api/profiles
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660513-4418493328: 307
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660540-4417849168: GET /api/profiles/
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660540-4417849168: 200
2025-07-07 10:57:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751860672560-4418588496: POST /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad/facebook-login
2025-07-07 10:57:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751860672560-4418588496: 200
2025-07-07 10:57:54 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:57:54 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:57:54 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:57:55 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:55 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:55 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:55 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:09:58 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398402-4659033104: GET /api/profiles
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398402-4659033104: 307
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398466-4659110288: GET /api/profiles/
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398466-4659110288: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408421-4659762128: OPTIONS /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408421-4659762128: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408433-4659667664: PUT /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408433-4659667664: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408468-4659808080: GET /api/profiles
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408468-4659808080: 307
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408510-4659766416: GET /api/profiles/
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408510-4659766416: 200
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411624-4659819408: GET /api/profiles
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411624-4659819408: 307
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411640-4659761616: GET /api/profiles/
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411640-4659761616: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425348-4659846672: OPTIONS /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425348-4659846672: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425365-4659851024: POST /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425365-4659851024: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425376-4660959568: OPTIONS /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425376-4660959568: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425386-4659852816: POST /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425386-4659852816: 201
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425406-4660998672: GET /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425406-4660998672: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425458-4661090960: GET /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425458-4661090960: 200
2025-07-07 11:10:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861428903-4660967312: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login
2025-07-07 11:10:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861428903-4660967312: 200
2025-07-07 11:10:31 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:10:31 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:10:31 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:10:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:10:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:10:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:10:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:11:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861511246-4401315024: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-terminate
2025-07-07 11:11:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861511246-4401315024: 404
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522876-4401412176: GET /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-status
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522876-4401412176: 200
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522964-4402043536: OPTIONS /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-complete
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522964-4402043536: 200
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522970-4402088464: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-complete
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522970-4402088464: 200
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861523023-4402091088: GET /api/profiles
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861523023-4402091088: 307
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861523058-4402100368: GET /api/profiles/
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861523058-4402100368: 200
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861528859-4402138640: GET /api/scraping/sessions
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861528864-4401413072: GET /api/scraping/sessions
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861528859-4402138640: 200
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861528864-4401413072: 200
2025-07-07 11:12:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861530927-4402099216: GET /api/scraping/sessions
2025-07-07 11:12:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861530927-4402099216: 200
2025-07-07 11:12:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861532976-4402204880: GET /api/scraping/sessions
2025-07-07 11:12:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861532976-4402204880: 200
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861534955-4402138896: OPTIONS /api/scraping/sessions/24
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861534955-4402138896: 200
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861534972-4402087824: DELETE /api/scraping/sessions/24
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861534972-4402087824: 200
2025-07-07 11:12:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861535000-4402161808: GET /api/scraping/sessions
2025-07-07 11:12:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861535000-4402161808: 200
2025-07-07 11:12:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861537029-4402350160: GET /api/scraping/sessions
2025-07-07 11:12:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861537029-4402350160: 200
2025-07-07 11:12:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861539082-4402094160: GET /api/scraping/sessions
2025-07-07 11:12:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861539082-4402094160: 200
2025-07-07 11:12:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861541129-4402348560: GET /api/scraping/sessions
2025-07-07 11:12:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861541129-4402348560: 200
2025-07-07 11:12:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861543179-4402165456: GET /api/scraping/sessions
2025-07-07 11:12:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861543179-4402165456: 200
2025-07-07 11:12:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861545226-4402358160: GET /api/scraping/sessions
2025-07-07 11:12:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861545226-4402358160: 200
2025-07-07 11:12:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861547271-4402141968: GET /api/scraping/sessions
2025-07-07 11:12:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861547271-4402141968: 200
2025-07-07 11:12:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861549318-4402362000: GET /api/scraping/sessions
2025-07-07 11:12:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861549318-4402362000: 200
2025-07-07 11:12:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861551364-4402349968: GET /api/scraping/sessions
2025-07-07 11:12:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861551364-4402349968: 200
2025-07-07 11:12:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861553409-4402211216: GET /api/scraping/sessions
2025-07-07 11:12:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861553409-4402211216: 200
2025-07-07 11:12:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861555457-4402152720: GET /api/scraping/sessions
2025-07-07 11:12:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861555457-4402152720: 200
2025-07-07 11:12:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861557503-4402363728: GET /api/scraping/sessions
2025-07-07 11:12:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861557503-4402363728: 200
2025-07-07 11:12:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861559554-4402167376: GET /api/scraping/sessions
2025-07-07 11:12:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861559554-4402167376: 200
2025-07-07 11:12:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861561599-4402362000: GET /api/scraping/sessions
2025-07-07 11:12:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861561599-4402362000: 200
2025-07-07 11:12:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861563647-4402352464: GET /api/scraping/sessions
2025-07-07 11:12:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861563647-4402352464: 200
2025-07-07 11:12:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861565695-4402163536: GET /api/scraping/sessions
2025-07-07 11:12:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861565695-4402163536: 200
2025-07-07 11:12:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861567740-4402348688: GET /api/scraping/sessions
2025-07-07 11:12:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861567740-4402348688: 200
2025-07-07 11:12:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861569785-4402152208: GET /api/scraping/sessions
2025-07-07 11:12:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861569785-4402152208: 200
2025-07-07 11:12:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861571833-4402205328: GET /api/scraping/sessions
2025-07-07 11:12:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861571833-4402205328: 200
2025-07-07 11:12:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861573881-4402162128: GET /api/scraping/sessions
2025-07-07 11:12:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861573881-4402162128: 200
2025-07-07 11:12:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861575926-4402164880: GET /api/scraping/sessions
2025-07-07 11:12:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861575926-4402164880: 200
2025-07-07 11:12:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861577974-4402354768: GET /api/scraping/sessions
2025-07-07 11:12:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861577974-4402354768: 200
2025-07-07 11:13:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751861580022-4402162448: GET /api/scraping/sessions
2025-07-07 11:13:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751861580022-4402162448: 200
2025-07-07 11:13:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861582068-4402095312: GET /api/scraping/sessions
2025-07-07 11:13:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861582068-4402095312: 200
2025-07-07 11:13:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751861584107-4402161360: GET /api/scraping/sessions
2025-07-07 11:13:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751861584107-4402161360: 200
2025-07-07 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861586153-4402360912: GET /api/scraping/sessions
2025-07-07 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861586153-4402360912: 200
2025-07-07 11:13:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861588198-4402165200: GET /api/scraping/sessions
2025-07-07 11:13:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861588198-4402165200: 200
2025-07-07 11:13:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861590243-4402207184: GET /api/scraping/sessions
2025-07-07 11:13:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861590243-4402207184: 200
2025-07-07 11:13:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861592288-4402356176: GET /api/scraping/sessions
2025-07-07 11:13:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861592288-4402356176: 200
2025-07-07 11:13:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861594335-4402358800: GET /api/scraping/sessions
2025-07-07 11:13:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861594335-4402358800: 200
2025-07-07 11:13:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861596386-4401409360: GET /api/scraping/sessions
2025-07-07 11:13:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861596386-4401409360: 200
2025-07-07 11:13:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861598433-4402163344: GET /api/scraping/sessions
2025-07-07 11:13:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861598433-4402163344: 200
2025-07-07 11:13:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861600469-4402349136: GET /api/scraping/sessions
2025-07-07 11:13:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861600469-4402349136: 200
2025-07-07 11:13:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861602513-4402361616: GET /api/scraping/sessions
2025-07-07 11:13:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861602513-4402361616: 200
2025-07-07 11:13:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861604560-4402140432: GET /api/scraping/sessions
2025-07-07 11:13:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861604560-4402140432: 200
2025-07-07 11:13:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861606606-4402161168: GET /api/scraping/sessions
2025-07-07 11:13:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861606606-4402161168: 200
2025-07-07 11:13:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861608652-4402087504: GET /api/scraping/sessions
2025-07-07 11:13:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861608652-4402087504: 200
2025-07-07 11:13:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861610698-4402358352: GET /api/scraping/sessions
2025-07-07 11:13:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861610698-4402358352: 200
2025-07-07 11:13:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861612745-4399467472: GET /api/scraping/sessions
2025-07-07 11:13:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861612745-4399467472: 200
2025-07-07 11:13:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861614790-4402205648: GET /api/scraping/sessions
2025-07-07 11:13:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861614790-4402205648: 200
2025-07-07 11:13:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861616832-4402363600: GET /api/scraping/sessions
2025-07-07 11:13:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861616832-4402363600: 200
2025-07-07 11:13:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861618875-4402364112: GET /api/scraping/sessions
2025-07-07 11:13:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861618875-4402364112: 200
2025-07-07 11:13:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861620920-4402214800: GET /api/scraping/sessions
2025-07-07 11:13:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861620920-4402214800: 200
2025-07-07 11:13:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861622951-4402092048: GET /api/scraping/sessions
2025-07-07 11:13:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861622951-4402092048: 200
2025-07-07 11:13:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861624996-4399292496: GET /api/scraping/sessions
2025-07-07 11:13:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861624996-4399292496: 200
2025-07-07 11:13:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861627085-4402361232: GET /api/scraping/sessions
2025-07-07 11:13:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861627085-4402361232: 200
2025-07-07 11:13:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861629131-4402087824: GET /api/scraping/sessions
2025-07-07 11:13:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861629131-4402087824: 200
2025-07-07 11:13:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861631176-4402166352: GET /api/scraping/sessions
2025-07-07 11:13:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861631176-4402166352: 200
2025-07-07 11:13:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861633222-4399467216: GET /api/scraping/sessions
2025-07-07 11:13:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861633222-4399467216: 200
2025-07-07 11:13:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861635270-4402356688: GET /api/scraping/sessions
2025-07-07 11:13:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861635270-4402356688: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637301-4402203408: GET /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637301-4402203408: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637676-4402374544: OPTIONS /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637676-4402374544: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637686-4402101584: POST /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637686-4402101584: 201
2025-07-07 11:13:57 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:13:57 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:13:57 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637727-4402363792: GET /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637727-4402363792: 200
2025-07-07 11:13:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861639748-4402444496: GET /api/scraping/sessions
2025-07-07 11:13:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861639748-4402444496: 200
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861641463-4402165200: POST /api/scraping/sessions/23/start
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861641463-4402165200: 200
2025-07-07 11:14:01 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:14:01 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861641492-4402373456: GET /api/scraping/sessions
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861641492-4402373456: 200
2025-07-07 11:14:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861643512-4402433232: GET /api/scraping/sessions
2025-07-07 11:14:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861643512-4402433232: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645556-4402367248: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645556-4402367248: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645915-4402446224: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645929-4402430288: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645932-4402376848: GET /api/profiles
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645935-4402504912: GET /api/profiles
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645932-4402376848: 307
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645935-4402504912: 307
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645915-4402446224: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645929-4402430288: 200
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861646028-4402374416: GET /api/profiles/
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861646032-4402508304: GET /api/profiles/
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861646032-4402508304: 200
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861646028-4402374416: 200
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648034-4402507984: GET /api/scraping/sessions
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648034-4402507984: 200
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648182-4402634448: POST /api/scraping/sessions/23/start
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648182-4402634448: 200
2025-07-07 11:14:08 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:14:08 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648207-4402498768: GET /api/scraping/sessions
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648207-4402498768: 200
2025-07-07 11:14:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861650233-4402642640: GET /api/scraping/sessions
2025-07-07 11:14:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861650233-4402642640: 200
2025-07-07 11:14:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861652270-4402636880: GET /api/scraping/sessions
2025-07-07 11:14:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861652270-4402636880: 200
2025-07-07 11:14:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861654310-4402634320: GET /api/scraping/sessions
2025-07-07 11:14:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861654310-4402634320: 200
2025-07-07 11:14:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861656345-4402377552: GET /api/scraping/sessions
2025-07-07 11:14:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861656345-4402377552: 200
2025-07-07 11:14:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861658389-4402627920: GET /api/scraping/sessions
2025-07-07 11:14:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861658389-4402627920: 200
2025-07-07 11:14:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861660434-4399659728: GET /api/scraping/sessions
2025-07-07 11:14:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861660434-4399659728: 200
2025-07-07 11:14:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861662476-4402638288: GET /api/scraping/sessions
2025-07-07 11:14:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861662476-4402638288: 200
2025-07-07 11:14:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861664531-4402634064: GET /api/scraping/sessions
2025-07-07 11:14:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861664531-4402634064: 200
2025-07-07 11:14:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861666571-4402507856: GET /api/scraping/sessions
2025-07-07 11:14:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861666571-4402507856: 200
2025-07-07 11:14:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861668612-4402163216: GET /api/scraping/sessions
2025-07-07 11:14:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861668612-4402163216: 200
2025-07-07 11:14:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861670658-4402366864: GET /api/scraping/sessions
2025-07-07 11:14:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861670658-4402366864: 200
2025-07-07 11:14:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861672699-4402633488: GET /api/scraping/sessions
2025-07-07 11:14:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861672699-4402633488: 200
2025-07-07 11:14:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861674746-4402634000: GET /api/scraping/sessions
2025-07-07 11:14:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861674746-4402634000: 200
2025-07-07 11:14:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861676786-4402443600: GET /api/scraping/sessions
2025-07-07 11:14:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861676786-4402443600: 200
2025-07-07 11:14:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861678825-4402365648: GET /api/scraping/sessions
2025-07-07 11:14:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861678825-4402365648: 200
2025-07-07 11:14:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861680872-4402634256: GET /api/scraping/sessions
2025-07-07 11:14:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861680872-4402634256: 200
2025-07-07 11:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861682913-4402633040: GET /api/scraping/sessions
2025-07-07 11:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861682913-4402633040: 200
2025-07-07 11:14:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861684955-4402369616: GET /api/scraping/sessions
2025-07-07 11:14:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861684955-4402369616: 200
2025-07-07 11:14:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861686995-4402375568: GET /api/scraping/sessions
2025-07-07 11:14:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861686995-4402375568: 200
2025-07-07 11:14:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861689031-4402631248: GET /api/scraping/sessions
2025-07-07 11:14:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861689031-4402631248: 200
2025-07-07 11:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861691066-4402443216: GET /api/scraping/sessions
2025-07-07 11:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861691066-4402443216: 200
2025-07-07 11:14:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861693101-4402433168: GET /api/scraping/sessions
2025-07-07 11:14:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861693101-4402433168: 200
2025-07-07 11:14:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861695133-4402380368: GET /api/scraping/sessions
2025-07-07 11:14:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861695133-4402380368: 200
2025-07-07 11:14:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861697169-4402495632: GET /api/scraping/sessions
2025-07-07 11:14:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861697169-4402495632: 200
2025-07-07 11:14:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861699212-4402095312: GET /api/scraping/sessions
2025-07-07 11:14:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861699212-4402095312: 200
2025-07-07 11:15:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861701256-4402367504: GET /api/scraping/sessions
2025-07-07 11:15:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861701256-4402367504: 200
2025-07-07 11:15:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861703303-4402369616: GET /api/scraping/sessions
2025-07-07 11:15:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861703303-4402369616: 200
2025-07-07 11:15:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861705347-4402496784: GET /api/scraping/sessions
2025-07-07 11:15:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861705347-4402496784: 200
2025-07-07 11:15:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861707389-4402506896: GET /api/scraping/sessions
2025-07-07 11:15:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861707389-4402506896: 200
2025-07-07 11:15:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861709428-4402505040: GET /api/scraping/sessions
2025-07-07 11:15:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861709428-4402505040: 200
2025-07-07 11:15:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861711473-4401402448: GET /api/scraping/sessions
2025-07-07 11:15:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861711473-4401402448: 200
2025-07-07 11:15:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861713518-4402642320: GET /api/scraping/sessions
2025-07-07 11:15:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861713518-4402642320: 200
2025-07-07 11:15:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861715555-4402498512: GET /api/scraping/sessions
2025-07-07 11:15:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861715555-4402498512: 200
2025-07-07 11:15:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861717599-4402162576: GET /api/scraping/sessions
2025-07-07 11:15:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861717599-4402162576: 200
2025-07-07 11:15:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861719643-4402507536: GET /api/scraping/sessions
2025-07-07 11:15:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861719643-4402507536: 200
2025-07-07 11:15:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861721688-4402638864: GET /api/scraping/sessions
2025-07-07 11:15:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861721688-4402638864: 200
2025-07-07 11:15:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861723733-4402374224: GET /api/scraping/sessions
2025-07-07 11:15:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861723733-4402374224: 200
2025-07-07 11:15:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861725778-4402638864: GET /api/scraping/sessions
2025-07-07 11:15:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861725778-4402638864: 200
2025-07-07 11:15:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861727820-4402101968: GET /api/scraping/sessions
2025-07-07 11:15:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861727820-4402101968: 200
2025-07-07 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861729864-4402439504: GET /api/scraping/sessions
2025-07-07 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861729864-4402439504: 200
2025-07-07 11:15:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861731904-4402364688: GET /api/scraping/sessions
2025-07-07 11:15:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861731904-4402364688: 200
2025-07-07 11:15:33 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:15:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861733948-4402629008: GET /api/scraping/sessions
2025-07-07 11:15:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861733948-4402629008: 200
2025-07-07 11:15:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861735989-4402152208: GET /api/scraping/sessions
2025-07-07 11:15:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861735989-4402152208: 200
2025-07-07 11:15:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861738032-4402442192: GET /api/scraping/sessions
2025-07-07 11:15:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861738032-4402442192: 200
2025-07-07 11:15:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861740076-4402432080: GET /api/scraping/sessions
2025-07-07 11:15:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861740076-4402432080: 200
2025-07-07 11:15:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861742121-4402437072: GET /api/scraping/sessions
2025-07-07 11:15:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861742121-4402437072: 200
2025-07-07 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861744160-4402361232: GET /api/scraping/sessions
2025-07-07 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861744160-4402361232: 200
2025-07-07 11:15:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861746198-4402437072: GET /api/scraping/sessions
2025-07-07 11:15:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861746198-4402437072: 200
2025-07-07 11:15:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861748238-4402444688: GET /api/scraping/sessions
2025-07-07 11:15:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861748238-4402444688: 200
2025-07-07 11:15:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861750271-4402638992: GET /api/scraping/sessions
2025-07-07 11:15:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861750271-4402638992: 200
2025-07-07 11:15:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861752315-4402437392: GET /api/scraping/sessions
2025-07-07 11:15:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861752315-4402437392: 200
2025-07-07 11:15:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861754358-4402502672: GET /api/scraping/sessions
2025-07-07 11:15:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861754358-4402502672: 200
2025-07-07 11:15:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751861756404-4402433808: GET /api/scraping/sessions
2025-07-07 11:15:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751861756404-4402433808: 200
2025-07-07 11:15:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861758460-4402634640: GET /api/scraping/sessions
2025-07-07 11:15:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861758460-4402634640: 200
2025-07-07 11:16:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751861760504-4402630480: GET /api/scraping/sessions
2025-07-07 11:16:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751861760504-4402630480: 200
2025-07-07 11:16:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861762551-4402161680: GET /api/scraping/sessions
2025-07-07 11:16:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861762551-4402161680: 200
2025-07-07 11:16:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751861764593-4402190352: GET /api/scraping/sessions
2025-07-07 11:16:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751861764593-4402190352: 200
2025-07-07 11:16:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861766634-4402635216: GET /api/scraping/sessions
2025-07-07 11:16:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861766634-4402635216: 200
2025-07-07 11:16:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861768678-4402629392: GET /api/scraping/sessions
2025-07-07 11:16:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861768678-4402629392: 200
2025-07-07 11:16:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861770721-4402363152: GET /api/scraping/sessions
2025-07-07 11:16:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861770721-4402363152: 200
2025-07-07 11:16:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861772764-4402641936: GET /api/scraping/sessions
2025-07-07 11:16:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861772764-4402641936: 200
2025-07-07 11:16:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861774807-4402371088: GET /api/scraping/sessions
2025-07-07 11:16:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861774807-4402371088: 200
2025-07-07 11:16:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861776845-4402641936: GET /api/scraping/sessions
2025-07-07 11:16:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861776845-4402641936: 200
2025-07-07 11:16:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861778881-4402628112: GET /api/scraping/sessions
2025-07-07 11:16:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861778881-4402628112: 200
2025-07-07 11:16:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861780918-4402636816: GET /api/scraping/sessions
2025-07-07 11:16:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861780918-4402636816: 200
2025-07-07 11:16:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861782962-4402432080: GET /api/scraping/sessions
2025-07-07 11:16:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861782962-4402432080: 200
2025-07-07 11:16:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861785005-4402637840: GET /api/scraping/sessions
2025-07-07 11:16:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861785005-4402637840: 200
2025-07-07 11:16:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861787048-4402626640: GET /api/scraping/sessions
2025-07-07 11:16:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861787048-4402626640: 200
2025-07-07 11:16:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861789088-4402633872: GET /api/scraping/sessions
2025-07-07 11:16:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861789088-4402633872: 200
2025-07-07 11:16:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861791124-4402368336: GET /api/scraping/sessions
2025-07-07 11:16:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861791124-4402368336: 200
2025-07-07 11:16:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861793176-4402366352: GET /api/scraping/sessions
2025-07-07 11:16:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861793176-4402366352: 200
2025-07-07 11:16:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861795219-4402352144: GET /api/scraping/sessions
2025-07-07 11:16:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861795219-4402352144: 200
2025-07-07 11:16:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861797263-4402435920: GET /api/scraping/sessions
2025-07-07 11:16:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861797263-4402435920: 200
2025-07-07 11:16:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861799301-4402629264: GET /api/scraping/sessions
2025-07-07 11:16:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861799301-4402629264: 200
2025-07-07 11:16:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861801348-4402367888: GET /api/scraping/sessions
2025-07-07 11:16:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861801348-4402367888: 200
2025-07-07 11:16:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861803377-4402365712: GET /api/scraping/sessions
2025-07-07 11:16:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861803377-4402365712: 200
2025-07-07 11:16:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861805414-4402510032: GET /api/scraping/sessions
2025-07-07 11:16:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861805414-4402510032: 200
2025-07-07 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861807461-4402631248: GET /api/scraping/sessions
2025-07-07 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861807461-4402631248: 200
2025-07-07 11:16:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861809503-4402141456: GET /api/scraping/sessions
2025-07-07 11:16:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861809503-4402141456: 200
2025-07-07 11:16:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861811539-4402378064: GET /api/scraping/sessions
2025-07-07 11:16:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861811539-4402378064: 200
2025-07-07 11:16:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861813585-4402046864: GET /api/scraping/sessions
2025-07-07 11:16:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861813585-4402046864: 200
2025-07-07 11:16:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861815621-4402507536: GET /api/scraping/sessions
2025-07-07 11:16:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861815621-4402507536: 200
2025-07-07 11:16:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861817663-4402099088: GET /api/scraping/sessions
2025-07-07 11:16:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861817663-4402099088: 200
2025-07-07 11:16:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861819709-4402631504: GET /api/scraping/sessions
2025-07-07 11:16:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861819709-4402631504: 200
2025-07-07 11:17:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861821753-4402369488: GET /api/scraping/sessions
2025-07-07 11:17:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861821753-4402369488: 200
2025-07-07 11:17:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861823795-4402638928: GET /api/scraping/sessions
2025-07-07 11:17:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861823795-4402638928: 200
2025-07-07 11:17:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861825836-4402637392: GET /api/scraping/sessions
2025-07-07 11:17:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861825836-4402637392: 200
2025-07-07 11:17:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861827872-4402371536: GET /api/scraping/sessions
2025-07-07 11:17:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861827872-4402371536: 200
2025-07-07 11:17:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861829917-4401085520: GET /api/scraping/sessions
2025-07-07 11:17:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861829917-4401085520: 200
2025-07-07 11:17:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861831961-4402629008: GET /api/scraping/sessions
2025-07-07 11:17:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861831961-4402629008: 200
2025-07-07 11:17:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861834001-4402627216: GET /api/scraping/sessions
2025-07-07 11:17:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861834001-4402627216: 200
2025-07-07 11:17:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861836045-4402372112: GET /api/scraping/sessions
2025-07-07 11:17:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861836045-4402372112: 200
2025-07-07 11:17:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861838089-4402440976: GET /api/scraping/sessions
2025-07-07 11:17:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861838089-4402440976: 200
2025-07-07 11:17:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861840149-4402378000: GET /api/scraping/sessions
2025-07-07 11:17:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861840149-4402378000: 200
2025-07-07 11:17:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861842217-4398835152: GET /api/scraping/sessions
2025-07-07 11:17:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861842217-4398835152: 200
2025-07-07 11:17:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861844255-4402503568: GET /api/scraping/sessions
2025-07-07 11:17:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861844255-4402503568: 200
2025-07-07 11:17:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861846296-4402372176: GET /api/scraping/sessions
2025-07-07 11:17:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861846296-4402372176: 200
2025-07-07 11:17:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861848339-4402640656: GET /api/scraping/sessions
2025-07-07 11:17:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861848339-4402640656: 200
2025-07-07 11:17:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861850380-4402377040: GET /api/scraping/sessions
2025-07-07 11:17:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861850380-4402377040: 200
2025-07-07 11:17:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861852421-4402629264: GET /api/scraping/sessions
2025-07-07 11:17:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861852421-4402629264: 200
2025-07-07 11:17:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861854461-4402190352: GET /api/scraping/sessions
2025-07-07 11:17:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861854461-4402190352: 200
2025-07-07 11:17:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861856503-4402440336: GET /api/scraping/sessions
2025-07-07 11:17:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861856503-4402440336: 200
2025-07-07 11:17:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861858546-4402144208: GET /api/scraping/sessions
2025-07-07 11:17:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861858546-4402144208: 200
2025-07-07 11:17:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861860590-4402628048: GET /api/scraping/sessions
2025-07-07 11:17:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861860590-4402628048: 200
2025-07-07 11:17:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861862633-4402044112: GET /api/scraping/sessions
2025-07-07 11:17:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861862633-4402044112: 200
2025-07-07 11:17:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861864678-4402164944: GET /api/scraping/sessions
2025-07-07 11:17:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861864678-4402164944: 200
2025-07-07 11:17:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861866723-4402095504: GET /api/scraping/sessions
2025-07-07 11:17:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861866723-4402095504: 200
2025-07-07 11:17:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861868763-4402635920: GET /api/scraping/sessions
2025-07-07 11:17:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861868763-4402635920: 200
2025-07-07 11:17:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861870800-4402354896: GET /api/scraping/sessions
2025-07-07 11:17:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861870800-4402354896: 200
2025-07-07 11:17:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861872836-4402442960: GET /api/scraping/sessions
2025-07-07 11:17:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861872836-4402442960: 200
2025-07-07 11:17:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861874880-4402634576: GET /api/scraping/sessions
2025-07-07 11:17:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861874880-4402634576: 200
2025-07-07 11:17:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751861876927-4402377936: GET /api/scraping/sessions
2025-07-07 11:17:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751861876927-4402377936: 200
2025-07-07 11:17:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861878970-4402157712: GET /api/scraping/sessions
2025-07-07 11:17:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861878970-4402157712: 200
2025-07-07 11:18:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861881026-4402436496: GET /api/scraping/sessions
2025-07-07 11:18:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861881026-4402436496: 200
2025-07-07 11:18:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861883071-4402372496: GET /api/scraping/sessions
2025-07-07 11:18:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861883071-4402372496: 200
2025-07-07 11:18:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861885116-4402632528: GET /api/scraping/sessions
2025-07-07 11:18:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861885116-4402632528: 200
2025-07-07 11:18:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861887162-4401984720: GET /api/scraping/sessions
2025-07-07 11:18:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861887162-4401984720: 200
2025-07-07 11:18:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861889209-4402442320: GET /api/scraping/sessions
2025-07-07 11:18:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861889209-4402442320: 200
2025-07-07 11:18:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861891246-4402375440: GET /api/scraping/sessions
2025-07-07 11:18:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861891246-4402375440: 200
2025-07-07 11:18:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861893289-4402216848: GET /api/scraping/sessions
2025-07-07 11:18:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861893289-4402216848: 200
2025-07-07 11:18:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861895333-4402440976: GET /api/scraping/sessions
2025-07-07 11:18:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861895333-4402440976: 200
2025-07-07 11:18:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861897380-4402086672: GET /api/scraping/sessions
2025-07-07 11:18:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861897380-4402086672: 200
2025-07-07 11:18:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861899429-4402627792: GET /api/scraping/sessions
2025-07-07 11:18:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861899429-4402627792: 200
2025-07-07 11:18:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861901475-4402496976: GET /api/scraping/sessions
2025-07-07 11:18:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861901475-4402496976: 200
2025-07-07 11:18:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861903529-4402634192: GET /api/scraping/sessions
2025-07-07 11:18:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861903529-4402634192: 200
2025-07-07 11:18:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861905571-4402442384: GET /api/scraping/sessions
2025-07-07 11:18:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861905571-4402442384: 200
2025-07-07 11:18:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861907608-4402369296: GET /api/scraping/sessions
2025-07-07 11:18:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861907608-4402369296: 200
2025-07-07 11:18:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861909653-4402634704: GET /api/scraping/sessions
2025-07-07 11:18:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861909653-4402634704: 200
2025-07-07 11:18:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861911693-4402437392: GET /api/scraping/sessions
2025-07-07 11:18:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861911693-4402437392: 200
2025-07-07 11:18:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861913741-4402502096: GET /api/scraping/sessions
2025-07-07 11:18:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861913741-4402502096: 200
2025-07-07 11:18:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861915782-4402632848: GET /api/scraping/sessions
2025-07-07 11:18:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861915782-4402632848: 200
2025-07-07 11:18:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861917815-4402440976: GET /api/scraping/sessions
2025-07-07 11:18:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861917815-4402440976: 200
2025-07-07 11:18:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861919857-4402135696: GET /api/scraping/sessions
2025-07-07 11:18:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861919857-4402135696: 200
2025-07-07 11:18:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861921900-4402432080: GET /api/scraping/sessions
2025-07-07 11:18:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861921900-4402432080: 200
2025-07-07 11:18:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861923947-4402376912: GET /api/scraping/sessions
2025-07-07 11:18:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861923947-4402376912: 200
2025-07-07 11:18:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861925991-4402672784: GET /api/scraping/sessions
2025-07-07 11:18:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861925991-4402672784: 200
2025-07-07 11:18:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861928034-4402511440: GET /api/scraping/sessions
2025-07-07 11:18:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861928034-4402511440: 200
2025-07-07 11:18:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861930080-4402668944: GET /api/scraping/sessions
2025-07-07 11:18:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861930080-4402668944: 200
2025-07-07 11:18:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861932125-4402634640: GET /api/scraping/sessions
2025-07-07 11:18:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861932125-4402634640: 200
2025-07-07 11:18:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861934172-4402674960: GET /api/scraping/sessions
2025-07-07 11:18:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861934172-4402674960: 200
2025-07-07 11:18:56 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:18:56 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:18:56 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:18:57 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:18:57 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:18:57 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:18:57 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:18:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861937421-4567810448: GET /api/scraping/sessions
2025-07-07 11:18:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861937421-4567810448: 200
2025-07-07 11:18:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861939459-4568397328: GET /api/scraping/sessions
2025-07-07 11:18:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861939459-4568397328: 200
2025-07-07 11:19:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861941503-4568452752: GET /api/scraping/sessions
2025-07-07 11:19:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861941503-4568452752: 200
2025-07-07 11:19:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861943544-4568463184: GET /api/scraping/sessions
2025-07-07 11:19:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861943544-4568463184: 200
2025-07-07 11:19:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861945588-4567820496: GET /api/scraping/sessions
2025-07-07 11:19:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861945588-4567820496: 200
2025-07-07 11:19:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861947636-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861947636-4568482640: 200
2025-07-07 11:19:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861949669-4568458064: GET /api/scraping/sessions
2025-07-07 11:19:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861949669-4568458064: 200
2025-07-07 11:19:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861951707-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861951707-4568482640: 200
2025-07-07 11:19:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861953752-4567812304: GET /api/scraping/sessions
2025-07-07 11:19:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861953752-4567812304: 200
2025-07-07 11:19:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861955797-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861955797-4568482640: 200
2025-07-07 11:19:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861957843-4568456656: GET /api/scraping/sessions
2025-07-07 11:19:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861957843-4568456656: 200
2025-07-07 11:19:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861959887-4568488016: GET /api/scraping/sessions
2025-07-07 11:19:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861959887-4568488016: 200
2025-07-07 11:19:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861961931-4568392592: GET /api/scraping/sessions
2025-07-07 11:19:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861961931-4568392592: 200
2025-07-07 11:19:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861963978-4568488400: GET /api/scraping/sessions
2025-07-07 11:19:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861963978-4568488400: 200
2025-07-07 11:19:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861966024-4567812048: GET /api/scraping/sessions
2025-07-07 11:19:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861966024-4567812048: 200
2025-07-07 11:19:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861968069-4568481872: GET /api/scraping/sessions
2025-07-07 11:19:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861968069-4568481872: 200
2025-07-07 11:19:29 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:19:29 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:19:29 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:19:30 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:19:30 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:19:30 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:19:30 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:19:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861970871-4471241360: GET /api/scraping/sessions
2025-07-07 11:19:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861970871-4471241360: 200
2025-07-07 11:19:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861972910-4471928208: GET /api/scraping/sessions
2025-07-07 11:19:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861972910-4471928208: 200
2025-07-07 11:19:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861974955-4471924432: GET /api/scraping/sessions
2025-07-07 11:19:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861974955-4471924432: 200
2025-07-07 11:19:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861976989-4471994000: GET /api/scraping/sessions
2025-07-07 11:19:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861976989-4471994000: 200
2025-07-07 11:19:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861979032-4471347600: GET /api/scraping/sessions
2025-07-07 11:19:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861979032-4471347600: 200
2025-07-07 11:19:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861981079-4472014992: GET /api/scraping/sessions
2025-07-07 11:19:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861981079-4472014992: 200
2025-07-07 11:19:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861983117-4465082576: GET /api/scraping/sessions
2025-07-07 11:19:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861983117-4465082576: 200
2025-07-07 11:19:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861985163-4472016656: GET /api/scraping/sessions
2025-07-07 11:19:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861985163-4472016656: 200
2025-07-07 11:19:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861987200-4471241488: GET /api/scraping/sessions
2025-07-07 11:19:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861987200-4471241488: 200
2025-07-07 11:19:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861989234-4472019344: GET /api/scraping/sessions
2025-07-07 11:19:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861989234-4472019344: 200
2025-07-07 11:19:50 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:19:50 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:19:50 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:23:39 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:23:39 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:23:39 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:23:39 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862219877-4753403344: GET /api/scraping/sessions
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862219879-4753465808: GET /api/scraping/sessions
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862219880-4753542544: GET /api/scraping/sessions
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862219879-4753465808: 200
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862219877-4753403344: 200
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862219880-4753542544: 200
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862219978-4753548688: GET /api/profiles
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862219978-4753548688: 307
2025-07-07 11:23:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862219998-4753406032: GET /api/profiles/
2025-07-07 11:23:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862219998-4753406032: 200
2025-07-07 11:23:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862221922-4753406096: GET /api/scraping/sessions
2025-07-07 11:23:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862221922-4753406096: 200
2025-07-07 11:23:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862223967-4754199440: GET /api/scraping/sessions
2025-07-07 11:23:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862223967-4754199440: 200
2025-07-07 11:23:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862226009-4754218640: GET /api/scraping/sessions
2025-07-07 11:23:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862226009-4754218640: 200
2025-07-07 11:23:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751862228050-4753546320: GET /api/scraping/sessions
2025-07-07 11:23:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751862228050-4753546320: 200
2025-07-07 11:23:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862230085-4754221008: GET /api/scraping/sessions
2025-07-07 11:23:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862230085-4754221008: 200
2025-07-07 11:23:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862232124-4753407760: GET /api/scraping/sessions
2025-07-07 11:23:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862232124-4753407760: 200
2025-07-07 11:23:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862234171-4754211600: GET /api/scraping/sessions
2025-07-07 11:23:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862234171-4754211600: 200
2025-07-07 11:23:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862236217-4754206224: GET /api/scraping/sessions
2025-07-07 11:23:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862236217-4754206224: 200
2025-07-07 11:23:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862238263-4754211664: GET /api/scraping/sessions
2025-07-07 11:23:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862238263-4754211664: 200
2025-07-07 11:24:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862240311-4753540112: GET /api/scraping/sessions
2025-07-07 11:24:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862240311-4753540112: 200
2025-07-07 11:24:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862242358-4754225488: GET /api/scraping/sessions
2025-07-07 11:24:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862242358-4754225488: 200
2025-07-07 11:24:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862244400-4754212304: GET /api/scraping/sessions
2025-07-07 11:24:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862244400-4754212304: 200
2025-07-07 11:24:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862246442-4753638224: GET /api/scraping/sessions
2025-07-07 11:24:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862246442-4753638224: 200
2025-07-07 11:24:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751862248481-4753547792: GET /api/scraping/sessions
2025-07-07 11:24:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751862248481-4753547792: 200
2025-07-07 11:24:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751862250514-4754226832: GET /api/scraping/sessions
2025-07-07 11:24:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751862250514-4754226832: 200
2025-07-07 11:24:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751862252563-4753402704: GET /api/scraping/sessions
2025-07-07 11:24:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751862252563-4753402704: 200
2025-07-07 11:24:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862254607-4754213904: GET /api/scraping/sessions
2025-07-07 11:24:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862254607-4754213904: 200
2025-07-07 11:24:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862256650-4754195408: GET /api/scraping/sessions
2025-07-07 11:24:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862256650-4754195408: 200
2025-07-07 11:24:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862258699-4754225168: GET /api/scraping/sessions
2025-07-07 11:24:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862258699-4754225168: 200
2025-07-07 11:24:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862260733-4753551760: GET /api/scraping/sessions
2025-07-07 11:24:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862260733-4753551760: 200
2025-07-07 11:24:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862262779-4754222480: GET /api/scraping/sessions
2025-07-07 11:24:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862262779-4754222480: 200
2025-07-07 11:24:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862264823-4754196112: GET /api/scraping/sessions
2025-07-07 11:24:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862264823-4754196112: 200
2025-07-07 11:24:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862266864-4754221136: GET /api/scraping/sessions
2025-07-07 11:24:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862266864-4754221136: 200
2025-07-07 11:24:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862268910-4754206224: GET /api/scraping/sessions
2025-07-07 11:24:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862268910-4754206224: 200
2025-07-07 11:24:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862270955-4754208976: GET /api/scraping/sessions
2025-07-07 11:24:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862270955-4754208976: 200
2025-07-07 11:24:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862272997-4753544592: GET /api/scraping/sessions
2025-07-07 11:24:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862272997-4753544592: 200
2025-07-07 11:24:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751862275042-4754225360: GET /api/scraping/sessions
2025-07-07 11:24:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751862275042-4754225360: 200
2025-07-07 11:24:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751862277081-4753465168: GET /api/scraping/sessions
2025-07-07 11:24:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862277081-4753465168: 200
2025-07-07 11:24:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862279117-4754206736: GET /api/scraping/sessions
2025-07-07 11:24:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862279117-4754206736: 200
2025-07-07 11:24:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862281159-4753400272: GET /api/scraping/sessions
2025-07-07 11:24:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862281159-4753400272: 200
2025-07-07 11:24:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862283204-4754224592: GET /api/scraping/sessions
2025-07-07 11:24:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862283204-4754224592: 200
2025-07-07 11:24:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862285260-4754224208: GET /api/scraping/sessions
2025-07-07 11:24:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862285260-4754224208: 200
2025-07-07 11:24:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862287298-4754211472: GET /api/scraping/sessions
2025-07-07 11:24:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862287298-4754211472: 200
2025-07-07 11:24:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862289337-4754212816: GET /api/scraping/sessions
2025-07-07 11:24:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862289337-4754212816: 200
2025-07-07 11:24:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862291371-4753403408: GET /api/scraping/sessions
2025-07-07 11:24:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862291371-4753403408: 200
2025-07-07 11:24:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862293414-4754203856: GET /api/scraping/sessions
2025-07-07 11:24:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862293414-4754203856: 200
2025-07-07 11:24:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862295461-4754223312: GET /api/scraping/sessions
2025-07-07 11:24:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862295461-4754223312: 200
2025-07-07 11:24:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751862297500-4753458256: GET /api/scraping/sessions
2025-07-07 11:24:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751862297500-4753458256: 200
2025-07-07 11:24:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862299545-4753550736: GET /api/scraping/sessions
2025-07-07 11:24:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862299545-4753550736: 200
2025-07-07 11:25:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862301585-4754203856: GET /api/scraping/sessions
2025-07-07 11:25:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862301585-4754203856: 200
2025-07-07 11:25:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862303626-4754231760: GET /api/scraping/sessions
2025-07-07 11:25:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862303626-4754231760: 200
2025-07-07 11:25:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862305668-4753544208: GET /api/scraping/sessions
2025-07-07 11:25:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862305668-4753544208: 200
2025-07-07 11:25:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862307714-4754239120: GET /api/scraping/sessions
2025-07-07 11:25:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862307714-4754239120: 200
2025-07-07 11:25:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862309756-4754216400: GET /api/scraping/sessions
2025-07-07 11:25:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862309756-4754216400: 200
2025-07-07 11:25:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862311799-4753406352: GET /api/scraping/sessions
2025-07-07 11:25:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862311799-4753406352: 200
2025-07-07 11:25:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862313839-4753403664: GET /api/scraping/sessions
2025-07-07 11:25:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862313839-4753403664: 200
2025-07-07 11:25:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862315880-4754224784: GET /api/scraping/sessions
2025-07-07 11:25:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862315880-4754224784: 200
2025-07-07 11:25:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862317921-4754209232: GET /api/scraping/sessions
2025-07-07 11:25:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862317921-4754209232: 200
2025-07-07 11:25:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862319963-4753550096: GET /api/scraping/sessions
2025-07-07 11:25:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862319963-4753550096: 200
2025-07-07 11:25:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862321998-4754227024: GET /api/scraping/sessions
2025-07-07 11:25:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862321998-4754227024: 200
2025-07-07 11:25:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862324041-4754230608: GET /api/scraping/sessions
2025-07-07 11:25:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862324041-4754230608: 200
2025-07-07 11:25:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862326082-4753466320: GET /api/scraping/sessions
2025-07-07 11:25:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862326082-4753466320: 200
2025-07-07 11:25:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862328123-4754236496: GET /api/scraping/sessions
2025-07-07 11:25:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862328123-4754236496: 200
2025-07-07 11:25:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862330163-4754211216: GET /api/scraping/sessions
2025-07-07 11:25:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862330163-4754211216: 200
2025-07-07 11:25:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862332203-4754230096: GET /api/scraping/sessions
2025-07-07 11:25:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862332203-4754230096: 200
2025-07-07 11:25:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862334245-4754217936: GET /api/scraping/sessions
2025-07-07 11:25:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862334245-4754217936: 200
2025-07-07 11:25:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862336291-4754233552: GET /api/scraping/sessions
2025-07-07 11:25:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862336291-4754233552: 200
2025-07-07 11:25:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862338335-4753400400: GET /api/scraping/sessions
2025-07-07 11:25:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862338335-4753400400: 200
2025-07-07 11:25:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751862340379-4754232720: GET /api/scraping/sessions
2025-07-07 11:25:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862340379-4754232720: 200
2025-07-07 11:25:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751862342424-4753403728: GET /api/scraping/sessions
2025-07-07 11:25:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751862342424-4753403728: 200
2025-07-07 11:25:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862344477-4754229968: GET /api/scraping/sessions
2025-07-07 11:25:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862344477-4754229968: 200
2025-07-07 11:25:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862346533-4753461392: GET /api/scraping/sessions
2025-07-07 11:25:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862346533-4753461392: 200
2025-07-07 11:25:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751862348579-4754227408: GET /api/scraping/sessions
2025-07-07 11:25:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751862348579-4754227408: 200
2025-07-07 11:25:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862350624-4753551760: GET /api/scraping/sessions
2025-07-07 11:25:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862350624-4753551760: 200
2025-07-07 11:25:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862352667-4754234256: GET /api/scraping/sessions
2025-07-07 11:25:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862352667-4754234256: 200
2025-07-07 11:25:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862354711-4753466960: GET /api/scraping/sessions
2025-07-07 11:25:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862354711-4753466960: 200
2025-07-07 11:25:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862356754-4754235472: GET /api/scraping/sessions
2025-07-07 11:25:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862356754-4754235472: 200
2025-07-07 11:25:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862358799-4753403728: GET /api/scraping/sessions
2025-07-07 11:25:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862358799-4753403728: 200
2025-07-07 11:26:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862360844-4754241808: GET /api/scraping/sessions
2025-07-07 11:26:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862360844-4754241808: 200
2025-07-07 11:26:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862362888-4754195088: GET /api/scraping/sessions
2025-07-07 11:26:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862362888-4754195088: 200
2025-07-07 11:26:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862364930-4754242320: GET /api/scraping/sessions
2025-07-07 11:26:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862364930-4754242320: 200
2025-07-07 11:26:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862366970-4753406608: GET /api/scraping/sessions
2025-07-07 11:26:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862366970-4753406608: 200
2025-07-07 11:26:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862369019-4754234128: GET /api/scraping/sessions
2025-07-07 11:26:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862369019-4754234128: 200
2025-07-07 11:26:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862371065-4754196560: GET /api/scraping/sessions
2025-07-07 11:26:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862371065-4754196560: 200
2025-07-07 11:26:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862373098-4754236496: GET /api/scraping/sessions
2025-07-07 11:26:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862373098-4754236496: 200
2025-07-07 11:26:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862375139-4753472464: GET /api/scraping/sessions
2025-07-07 11:26:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862375139-4753472464: 200
2025-07-07 11:26:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862377184-4754229584: GET /api/scraping/sessions
2025-07-07 11:26:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862377184-4754229584: 200
2025-07-07 11:26:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862379226-4754206096: GET /api/scraping/sessions
2025-07-07 11:26:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862379226-4754206096: 200
2025-07-07 11:26:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862381270-4754231888: GET /api/scraping/sessions
2025-07-07 11:26:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751862381270-4754231888: 200
2025-07-07 11:26:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751862383316-4753460944: GET /api/scraping/sessions
2025-07-07 11:26:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751862383316-4753460944: 200
2025-07-07 11:26:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751862385359-4754231248: GET /api/scraping/sessions
2025-07-07 11:26:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751862385359-4754231248: 200
2025-07-07 11:26:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862387401-4754224592: GET /api/scraping/sessions
2025-07-07 11:26:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862387401-4754224592: 200
2025-07-07 11:26:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751862389441-4754240912: GET /api/scraping/sessions
2025-07-07 11:26:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751862389441-4754240912: 200
2025-07-07 11:26:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751862391480-4753399824: GET /api/scraping/sessions
2025-07-07 11:26:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751862391480-4753399824: 200
2025-07-07 11:26:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751862393523-4754227280: GET /api/scraping/sessions
2025-07-07 11:26:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862393523-4754227280: 200
2025-07-07 11:26:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751862395560-4754242320: GET /api/scraping/sessions
2025-07-07 11:26:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751862395560-4754242320: 200
2025-07-07 11:26:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751862397595-4754205840: GET /api/scraping/sessions
2025-07-07 11:26:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862397595-4754205840: 200
2025-07-07 11:26:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862399633-4754227344: GET /api/scraping/sessions
2025-07-07 11:26:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862399633-4754227344: 200
2025-07-07 11:26:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862401676-4754242256: GET /api/scraping/sessions
2025-07-07 11:26:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862401676-4754242256: 200
2025-07-07 11:26:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862403720-4754237392: GET /api/scraping/sessions
2025-07-07 11:26:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862403720-4754237392: 200
2025-07-07 11:26:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862405761-4753402704: GET /api/scraping/sessions
2025-07-07 11:26:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862405761-4753402704: 200
2025-07-07 11:26:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862407803-4754128848: GET /api/scraping/sessions
2025-07-07 11:26:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862407803-4754128848: 200
2025-07-07 11:26:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862409845-4754234256: GET /api/scraping/sessions
2025-07-07 11:26:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862409845-4754234256: 200
2025-07-07 11:26:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862411889-4754234960: GET /api/scraping/sessions
2025-07-07 11:26:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862411889-4754234960: 200
2025-07-07 11:26:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862413928-4754199760: GET /api/scraping/sessions
2025-07-07 11:26:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862413928-4754199760: 200
2025-07-07 11:26:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862415970-4753460496: GET /api/scraping/sessions
2025-07-07 11:26:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862415970-4753460496: 200
2025-07-07 11:26:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862418013-4754242064: GET /api/scraping/sessions
2025-07-07 11:26:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862418013-4754242064: 200
2025-07-07 11:27:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862420057-4754239056: GET /api/scraping/sessions
2025-07-07 11:27:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862420057-4754239056: 200
2025-07-07 11:27:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862422100-4754204688: GET /api/scraping/sessions
2025-07-07 11:27:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862422100-4754204688: 200
2025-07-07 11:27:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862424134-4753472784: GET /api/scraping/sessions
2025-07-07 11:27:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862424134-4753472784: 200
2025-07-07 11:27:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862426175-4754238288: GET /api/scraping/sessions
2025-07-07 11:27:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862426175-4754238288: 200
2025-07-07 11:27:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751862428220-4753543824: GET /api/scraping/sessions
2025-07-07 11:27:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751862428220-4753543824: 200
2025-07-07 11:27:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751862430261-4754235472: GET /api/scraping/sessions
2025-07-07 11:27:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751862430261-4754235472: 200
2025-07-07 11:27:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751862432300-4754207696: GET /api/scraping/sessions
2025-07-07 11:27:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751862432300-4754207696: 200
2025-07-07 11:27:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862434339-4754235600: GET /api/scraping/sessions
2025-07-07 11:27:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862434339-4754235600: 200
2025-07-07 11:27:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862436383-4754231184: GET /api/scraping/sessions
2025-07-07 11:27:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862436383-4754231184: 200
2025-07-07 11:27:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862438426-4753549200: GET /api/scraping/sessions
2025-07-07 11:27:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862438426-4753549200: 200
2025-07-07 11:27:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862440461-4754203600: GET /api/scraping/sessions
2025-07-07 11:27:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862440461-4754203600: 200
2025-07-07 11:27:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862442502-4754203600: GET /api/scraping/sessions
2025-07-07 11:27:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862442502-4754203600: 200
2025-07-07 11:27:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862444544-4745446416: GET /api/scraping/sessions
2025-07-07 11:27:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862444544-4745446416: 200
2025-07-07 11:27:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862446586-4754208080: GET /api/scraping/sessions
2025-07-07 11:27:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862446586-4754208080: 200
2025-07-07 11:27:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862448627-4753243856: GET /api/scraping/sessions
2025-07-07 11:27:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862448627-4753243856: 200
2025-07-07 11:27:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862450670-4754233232: GET /api/scraping/sessions
2025-07-07 11:27:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862450670-4754233232: 200
2025-07-07 11:27:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862452715-4753406544: GET /api/scraping/sessions
2025-07-07 11:27:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862452715-4753406544: 200
2025-07-07 11:27:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862454760-4753461840: GET /api/scraping/sessions
2025-07-07 11:27:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862454760-4753461840: 200
2025-07-07 11:27:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862456804-4754232208: GET /api/scraping/sessions
2025-07-07 11:27:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862456804-4754232208: 200
2025-07-07 11:27:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862458848-4754217296: GET /api/scraping/sessions
2025-07-07 11:27:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862458848-4754217296: 200
2025-07-07 11:27:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751862460890-4753407760: GET /api/scraping/sessions
2025-07-07 11:27:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862460890-4753407760: 200
2025-07-07 11:27:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751862462932-4754239440: GET /api/scraping/sessions
2025-07-07 11:27:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751862462932-4754239440: 200
2025-07-07 11:27:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862464974-4754222992: GET /api/scraping/sessions
2025-07-07 11:27:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862464974-4754222992: 200
2025-07-07 11:27:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862467018-4754239952: GET /api/scraping/sessions
2025-07-07 11:27:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862467018-4754239952: 200
2025-07-07 11:27:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862469057-4753548496: GET /api/scraping/sessions
2025-07-07 11:27:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862469057-4753548496: 200
2025-07-07 11:27:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862471099-4754233936: GET /api/scraping/sessions
2025-07-07 11:27:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862471099-4754233936: 200
2025-07-07 11:27:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862473140-4753403664: GET /api/scraping/sessions
2025-07-07 11:27:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862473140-4753403664: 200
2025-07-07 11:27:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862475182-4754230096: GET /api/scraping/sessions
2025-07-07 11:27:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862475182-4754230096: 200
2025-07-07 11:27:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751862477223-4754242128: GET /api/scraping/sessions
2025-07-07 11:27:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751862477223-4754242128: 200
2025-07-07 11:27:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862479267-4754224080: GET /api/scraping/sessions
2025-07-07 11:27:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862479267-4754224080: 200
2025-07-07 11:28:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862481308-4753399056: GET /api/scraping/sessions
2025-07-07 11:28:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862481308-4753399056: 200
2025-07-07 11:28:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862483351-4754225488: GET /api/scraping/sessions
2025-07-07 11:28:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862483351-4754225488: 200
2025-07-07 11:28:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862485393-4753461840: GET /api/scraping/sessions
2025-07-07 11:28:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862485393-4753461840: 200
2025-07-07 11:28:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862487434-4754232848: GET /api/scraping/sessions
2025-07-07 11:28:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862487434-4754232848: 200
2025-07-07 11:28:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862489475-4754121424: GET /api/scraping/sessions
2025-07-07 11:28:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862489475-4754121424: 200
2025-07-07 11:28:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862491513-4754237648: GET /api/scraping/sessions
2025-07-07 11:28:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862491513-4754237648: 200
2025-07-07 11:28:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862493556-4754227280: GET /api/scraping/sessions
2025-07-07 11:28:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862493556-4754227280: 200
2025-07-07 11:28:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862495601-4753472144: GET /api/scraping/sessions
2025-07-07 11:28:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862495601-4753472144: 200
2025-07-07 11:28:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862497646-4753399056: GET /api/scraping/sessions
2025-07-07 11:28:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862497646-4753399056: 200
2025-07-07 11:28:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862499689-4754237008: GET /api/scraping/sessions
2025-07-07 11:28:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862499689-4754237008: 200
2025-07-07 11:28:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862501734-4754227856: GET /api/scraping/sessions
2025-07-07 11:28:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751862501734-4754227856: 200
2025-07-07 11:28:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751862503776-4753547280: GET /api/scraping/sessions
2025-07-07 11:28:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751862503776-4753547280: 200
2025-07-07 11:28:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751862505822-4754240336: GET /api/scraping/sessions
2025-07-07 11:28:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751862505822-4754240336: 200
2025-07-07 11:28:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862507866-4754211280: GET /api/scraping/sessions
2025-07-07 11:28:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862507866-4754211280: 200
2025-07-07 11:28:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751862509908-4754226384: GET /api/scraping/sessions
2025-07-07 11:28:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751862509908-4754226384: 200
2025-07-07 11:28:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751862511947-4754231824: GET /api/scraping/sessions
2025-07-07 11:28:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751862511947-4754231824: 200
2025-07-07 11:28:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751862513989-4753466320: GET /api/scraping/sessions
2025-07-07 11:28:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862513989-4753466320: 200
2025-07-07 11:28:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862516035-4754242512: GET /api/scraping/sessions
2025-07-07 11:28:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862516035-4754242512: 200
2025-07-07 11:28:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862518079-4754239568: GET /api/scraping/sessions
2025-07-07 11:28:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862518079-4754239568: 200
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519572-4754218640: GET /api/profiles
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519574-4754220816: GET /api/profiles
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519575-4754228048: GET /api/scraping/sessions
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519572-4754218640: 307
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519578-4765785488: GET /api/scraping/sessions
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519574-4754220816: 307
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519586-4765876880: GET /api/profiles/
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862519588-4765944912: GET /api/profiles/
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519578-4765785488: 200
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519575-4754228048: 200
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519586-4765876880: 200
2025-07-07 11:28:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862519588-4765944912: 200
2025-07-07 11:28:41 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:28:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862521606-4753405520: POST /api/scraping/sessions/23/start
2025-07-07 11:28:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862521606-4753405520: 200
2025-07-07 11:28:41 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:28:41 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:28:41 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:28:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862521648-4765882320: GET /api/scraping/sessions
2025-07-07 11:28:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862521648-4765882320: 200
2025-07-07 11:28:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862523672-4765953744: GET /api/scraping/sessions
2025-07-07 11:28:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862523672-4765953744: 200
2025-07-07 11:28:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862525719-4765953744: GET /api/scraping/sessions
2025-07-07 11:28:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862525719-4765953744: 200
2025-07-07 11:28:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862527760-4765891600: GET /api/scraping/sessions
2025-07-07 11:28:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862527760-4765891600: 200
2025-07-07 11:28:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862529806-4753468176: GET /api/scraping/sessions
2025-07-07 11:28:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862529806-4753468176: 200
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530297-4753461392: GET /api/profiles
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530301-4753397136: GET /api/profiles
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530304-4748148432: GET /api/scraping/sessions
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530305-4754225744: GET /api/scraping/sessions
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530297-4753461392: 307
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530301-4753397136: 307
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530304-4748148432: 200
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530335-4765984336: GET /api/profiles/
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862530336-4765988432: GET /api/profiles/
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530305-4754225744: 200
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530336-4765988432: 200
2025-07-07 11:28:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862530335-4765984336: 200
2025-07-07 11:28:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862532398-4754218512: GET /api/scraping/sessions
2025-07-07 11:28:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862532398-4754218512: 200
2025-07-07 11:28:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862534449-4765880144: GET /api/scraping/sessions
2025-07-07 11:28:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862534449-4765880144: 200
2025-07-07 11:28:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862536489-4753397072: GET /api/scraping/sessions
2025-07-07 11:28:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862536489-4753397072: 200
2025-07-07 11:28:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862538537-4754232784: GET /api/scraping/sessions
2025-07-07 11:28:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862538537-4754232784: 200
2025-07-07 11:29:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862540584-4754225552: GET /api/scraping/sessions
2025-07-07 11:29:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862540584-4754225552: 200
2025-07-07 11:29:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862542616-4765981264: GET /api/scraping/sessions
2025-07-07 11:29:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862542616-4765981264: 200
2025-07-07 11:29:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862544664-4754223376: GET /api/scraping/sessions
2025-07-07 11:29:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862544664-4754223376: 200
2025-07-07 11:29:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862546713-4754235280: GET /api/scraping/sessions
2025-07-07 11:29:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862546713-4754235280: 200
2025-07-07 11:29:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751862548759-4765986128: GET /api/scraping/sessions
2025-07-07 11:29:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751862548759-4765986128: 200
2025-07-07 11:29:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751862550806-4754239312: GET /api/scraping/sessions
2025-07-07 11:29:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751862550806-4754239312: 200
2025-07-07 11:29:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751862552846-4754227728: GET /api/scraping/sessions
2025-07-07 11:29:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751862552846-4754227728: 200
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862553579-4765878544: OPTIONS /api/scraping/sessions
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862553579-4765878544: 200
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862553590-4753540112: POST /api/scraping/sessions
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862553590-4753540112: 201
2025-07-07 11:29:13 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:29:13 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862553623-4765891408: GET /api/scraping/sessions
2025-07-07 11:29:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862553623-4765891408: 200
2025-07-07 11:29:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862555649-4765988688: GET /api/scraping/sessions
2025-07-07 11:29:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862555649-4765988688: 200
2025-07-07 11:29:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862557696-4754223184: GET /api/scraping/sessions
2025-07-07 11:29:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862557696-4754223184: 200
2025-07-07 11:29:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862559739-4765881680: GET /api/scraping/sessions
2025-07-07 11:29:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862559739-4765881680: 200
2025-07-07 11:29:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862561773-4765785488: GET /api/scraping/sessions
2025-07-07 11:29:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751862561773-4765785488: 200
2025-07-07 11:29:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751862563819-4754204688: GET /api/scraping/sessions
2025-07-07 11:29:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751862563819-4754204688: 200
2025-07-07 11:29:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751862565856-4765784400: GET /api/scraping/sessions
2025-07-07 11:29:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751862565856-4765784400: 200
2025-07-07 11:29:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862567005-4765977552: POST /api/scraping/sessions/24/start
2025-07-07 11:29:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862567005-4765977552: 200
2025-07-07 11:29:27 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:29:27 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:29:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862567039-4754239568: GET /api/scraping/sessions
2025-07-07 11:29:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862567039-4754239568: 200
2025-07-07 11:29:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751862569064-4753392016: GET /api/scraping/sessions
2025-07-07 11:29:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751862569064-4753392016: 200
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570400-4765989776: GET /api/scraping/sessions
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570410-4754230224: GET /api/scraping/sessions
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570414-4754242960: GET /api/profiles
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570415-4765955792: GET /api/profiles
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570414-4754242960: 307
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570415-4765955792: 307
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570410-4754230224: 200
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570400-4765989776: 200
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570440-4765890512: GET /api/profiles/
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862570442-4766178960: GET /api/profiles/
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570440-4765890512: 200
2025-07-07 11:29:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862570442-4766178960: 200
2025-07-07 11:29:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862572445-4766174672: GET /api/scraping/sessions
2025-07-07 11:29:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862572445-4766174672: 200
2025-07-07 11:29:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862574496-4754222288: GET /api/scraping/sessions
2025-07-07 11:29:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862574496-4754222288: 200
2025-07-07 11:29:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862576547-4765946704: GET /api/scraping/sessions
2025-07-07 11:29:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862576547-4765946704: 200
2025-07-07 11:29:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862578584-4765888848: GET /api/scraping/sessions
2025-07-07 11:29:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862578584-4765888848: 200
2025-07-07 11:29:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751862580628-4754232400: GET /api/scraping/sessions
2025-07-07 11:29:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862580628-4754232400: 200
2025-07-07 11:29:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751862582665-4766175824: GET /api/scraping/sessions
2025-07-07 11:29:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751862582665-4766175824: 200
2025-07-07 11:29:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862584703-4765793360: GET /api/scraping/sessions
2025-07-07 11:29:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862584703-4765793360: 200
2025-07-07 11:29:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862586748-4766186192: GET /api/scraping/sessions
2025-07-07 11:29:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862586748-4766186192: 200
2025-07-07 11:29:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751862588794-4748148432: GET /api/scraping/sessions
2025-07-07 11:29:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751862588794-4748148432: 200
2025-07-07 11:29:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862590840-4766173904: GET /api/scraping/sessions
2025-07-07 11:29:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862590840-4766173904: 200
2025-07-07 11:29:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862592881-4765988304: GET /api/scraping/sessions
2025-07-07 11:29:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862592881-4765988304: 200
2025-07-07 11:29:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862594926-4766187024: GET /api/scraping/sessions
2025-07-07 11:29:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862594926-4766187024: 200
2025-07-07 11:29:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862596970-4765950544: GET /api/scraping/sessions
2025-07-07 11:29:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862596970-4765950544: 200
2025-07-07 11:29:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862599018-4766183440: GET /api/scraping/sessions
2025-07-07 11:29:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862599018-4766183440: 200
2025-07-07 11:30:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862601063-4765981584: GET /api/scraping/sessions
2025-07-07 11:30:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862601063-4765981584: 200
2025-07-07 11:30:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862603111-4766181904: GET /api/scraping/sessions
2025-07-07 11:30:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862603111-4766181904: 200
2025-07-07 11:30:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862605158-4765977296: GET /api/scraping/sessions
2025-07-07 11:30:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862605158-4765977296: 200
2025-07-07 11:30:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862607213-4766185552: GET /api/scraping/sessions
2025-07-07 11:30:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862607213-4766185552: 200
2025-07-07 11:30:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862609260-4765778576: GET /api/scraping/sessions
2025-07-07 11:30:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862609260-4765778576: 200
2025-07-07 11:30:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862611300-4766187216: GET /api/scraping/sessions
2025-07-07 11:30:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862611300-4766187216: 200
2025-07-07 11:30:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862613350-4753547280: GET /api/scraping/sessions
2025-07-07 11:30:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862613350-4753547280: 200
2025-07-07 11:30:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862615397-4766178256: GET /api/scraping/sessions
2025-07-07 11:30:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862615397-4766178256: 200
2025-07-07 11:30:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862617456-4754229648: GET /api/scraping/sessions
2025-07-07 11:30:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862617456-4754229648: 200
2025-07-07 11:30:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862619500-4766181648: GET /api/scraping/sessions
2025-07-07 11:30:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862619500-4766181648: 200
2025-07-07 11:30:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862621548-4753546384: GET /api/scraping/sessions
2025-07-07 11:30:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751862621548-4753546384: 200
2025-07-07 11:30:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751862623590-4766186704: GET /api/scraping/sessions
2025-07-07 11:30:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751862623590-4766186704: 200
2025-07-07 11:30:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751862625631-4754201552: GET /api/scraping/sessions
2025-07-07 11:30:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751862625631-4754201552: 200
2025-07-07 11:30:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862627671-4766185040: GET /api/scraping/sessions
2025-07-07 11:30:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862627671-4766185040: 200
2025-07-07 11:30:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751862629707-4766183440: GET /api/scraping/sessions
2025-07-07 11:30:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751862629707-4766183440: 200
2025-07-07 11:30:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751862631753-4765793744: GET /api/scraping/sessions
2025-07-07 11:30:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751862631753-4765793744: 200
2025-07-07 11:30:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751862633798-4754128592: GET /api/scraping/sessions
2025-07-07 11:30:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862633798-4754128592: 200
2025-07-07 11:30:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751862635838-4766173328: GET /api/scraping/sessions
2025-07-07 11:30:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751862635838-4766173328: 200
2025-07-07 11:30:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751862637876-4753402704: GET /api/scraping/sessions
2025-07-07 11:30:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862637876-4753402704: 200
2025-07-07 11:30:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862639923-4766177936: GET /api/scraping/sessions
2025-07-07 11:30:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862639923-4766177936: 200
2025-07-07 11:30:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862641965-4766187344: GET /api/scraping/sessions
2025-07-07 11:30:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862641965-4766187344: 200
2025-07-07 11:30:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862644016-4765976144: GET /api/scraping/sessions
2025-07-07 11:30:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862644016-4765976144: 200
2025-07-07 11:30:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862646062-4765781648: GET /api/scraping/sessions
2025-07-07 11:30:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862646062-4765781648: 200
2025-07-07 11:30:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751862648107-4766186256: GET /api/scraping/sessions
2025-07-07 11:30:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751862648107-4766186256: 200
2025-07-07 11:30:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862650153-4765887248: GET /api/scraping/sessions
2025-07-07 11:30:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862650153-4765887248: 200
2025-07-07 11:30:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862652203-4765983440: GET /api/scraping/sessions
2025-07-07 11:30:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862652203-4765983440: 200
2025-07-07 11:30:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862654245-4754233232: GET /api/scraping/sessions
2025-07-07 11:30:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862654245-4754233232: 200
2025-07-07 11:30:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862656288-4766172368: GET /api/scraping/sessions
2025-07-07 11:30:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862656288-4766172368: 200
2025-07-07 11:30:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862658344-4765952848: GET /api/scraping/sessions
2025-07-07 11:30:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862658344-4765952848: 200
2025-07-07 11:31:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862660371-4766181328: GET /api/scraping/sessions
2025-07-07 11:31:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862660371-4766181328: 200
2025-07-07 11:31:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862662414-4765781648: GET /api/scraping/sessions
2025-07-07 11:31:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862662414-4765781648: 200
2025-07-07 11:31:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862664462-4765989584: GET /api/scraping/sessions
2025-07-07 11:31:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862664462-4765989584: 200
2025-07-07 11:31:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862666507-4753547728: GET /api/scraping/sessions
2025-07-07 11:31:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862666507-4753547728: 200
2025-07-07 11:31:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751862668552-4766171920: GET /api/scraping/sessions
2025-07-07 11:31:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751862668552-4766171920: 200
2025-07-07 11:31:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751862670597-4754227856: GET /api/scraping/sessions
2025-07-07 11:31:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751862670597-4754227856: 200
2025-07-07 11:31:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751862672640-4766178704: GET /api/scraping/sessions
2025-07-07 11:31:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751862672640-4766178704: 200
2025-07-07 11:31:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862674687-4754200016: GET /api/scraping/sessions
2025-07-07 11:31:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862674687-4754200016: 200
2025-07-07 11:31:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862676730-4766183376: GET /api/scraping/sessions
2025-07-07 11:31:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862676730-4766183376: 200
2025-07-07 11:31:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862678776-4765990480: GET /api/scraping/sessions
2025-07-07 11:31:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862678776-4765990480: 200
2025-07-07 11:31:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862680819-4766181968: GET /api/scraping/sessions
2025-07-07 11:31:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862680819-4766181968: 200
2025-07-07 11:31:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862682862-4766184144: GET /api/scraping/sessions
2025-07-07 11:31:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862682862-4766184144: 200
2025-07-07 11:31:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862684907-4766184784: GET /api/scraping/sessions
2025-07-07 11:31:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862684907-4766184784: 200
2025-07-07 11:31:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862686943-4754233936: GET /api/scraping/sessions
2025-07-07 11:31:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862686943-4754233936: 200
2025-07-07 11:31:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862688981-4765978192: GET /api/scraping/sessions
2025-07-07 11:31:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862688981-4765978192: 200
2025-07-07 11:31:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751862691025-4766175696: GET /api/scraping/sessions
2025-07-07 11:31:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751862691025-4766175696: 200
2025-07-07 11:31:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751862693072-4766186512: GET /api/scraping/sessions
2025-07-07 11:31:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862693072-4766186512: 200
2025-07-07 11:31:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751862695117-4754234256: GET /api/scraping/sessions
2025-07-07 11:31:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751862695117-4754234256: 200
2025-07-07 11:31:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751862697163-4766181904: GET /api/scraping/sessions
2025-07-07 11:31:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862697163-4766181904: 200
2025-07-07 11:31:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862699205-4766183312: GET /api/scraping/sessions
2025-07-07 11:31:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862699205-4766183312: 200
2025-07-07 11:31:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862701250-4766186640: GET /api/scraping/sessions
2025-07-07 11:31:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862701250-4766186640: 200
2025-07-07 11:31:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862703296-4765978128: GET /api/scraping/sessions
2025-07-07 11:31:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862703296-4765978128: 200
2025-07-07 11:31:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862705343-4766177232: GET /api/scraping/sessions
2025-07-07 11:31:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862705343-4766177232: 200
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862707385-4766187408: GET /api/scraping/sessions
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862707385-4766187408: 200
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862707900-4754196752: POST /api/scraping/sessions/24/start
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862707900-4754196752: 200
2025-07-07 11:31:47 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:31:47 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862707936-4765985552: GET /api/scraping/sessions
2025-07-07 11:31:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862707936-4765985552: 200
2025-07-07 11:31:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862709995-4766177040: GET /api/scraping/sessions
2025-07-07 11:31:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862709995-4766177040: 200
2025-07-07 11:31:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862712037-4766187024: GET /api/scraping/sessions
2025-07-07 11:31:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862712037-4766187024: 200
2025-07-07 11:31:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862714081-4765785168: GET /api/scraping/sessions
2025-07-07 11:31:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862714081-4765785168: 200
2025-07-07 11:31:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862716127-4765952720: GET /api/scraping/sessions
2025-07-07 11:31:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862716127-4765952720: 200
2025-07-07 11:31:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862718164-4766171344: GET /api/scraping/sessions
2025-07-07 11:31:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751862718164-4766171344: 200
2025-07-07 11:32:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751862720210-4766175312: GET /api/scraping/sessions
2025-07-07 11:32:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751862720210-4766175312: 200
2025-07-07 11:32:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751862722255-4765791184: GET /api/scraping/sessions
2025-07-07 11:32:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751862722255-4765791184: 200
2025-07-07 11:32:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751862724300-4748444432: GET /api/scraping/sessions
2025-07-07 11:32:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751862724300-4748444432: 200
2025-07-07 11:32:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751862726336-4766179152: GET /api/scraping/sessions
2025-07-07 11:32:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751862726336-4766179152: 200
2025-07-07 11:32:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751862728381-4766176720: GET /api/scraping/sessions
2025-07-07 11:32:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751862728381-4766176720: 200
2025-07-07 11:32:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751862730423-4754230928: GET /api/scraping/sessions
2025-07-07 11:32:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751862730423-4754230928: 200
2025-07-07 11:32:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751862732468-4765886288: GET /api/scraping/sessions
2025-07-07 11:32:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751862732468-4765886288: 200
2025-07-07 11:32:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862734513-4766184848: GET /api/scraping/sessions
2025-07-07 11:32:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862734513-4766184848: 200
2025-07-07 11:32:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862736560-4766186384: GET /api/scraping/sessions
2025-07-07 11:32:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862736560-4766186384: 200
2025-07-07 11:32:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862738605-4765785744: GET /api/scraping/sessions
2025-07-07 11:32:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862738605-4765785744: 200
2025-07-07 11:32:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862740648-4765946000: GET /api/scraping/sessions
2025-07-07 11:32:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862740648-4765946000: 200
2025-07-07 11:32:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862742691-4766172944: GET /api/scraping/sessions
2025-07-07 11:32:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862742691-4766172944: 200
2025-07-07 11:32:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862744735-4748559120: GET /api/scraping/sessions
2025-07-07 11:32:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862744735-4748559120: 200
2025-07-07 11:32:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862746778-4753402704: GET /api/scraping/sessions
2025-07-07 11:32:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862746778-4753402704: 200
2025-07-07 11:32:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862748828-4765778960: GET /api/scraping/sessions
2025-07-07 11:32:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862748828-4765778960: 200
2025-07-07 11:32:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862750872-4766187408: GET /api/scraping/sessions
2025-07-07 11:32:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862750872-4766187408: 200
2025-07-07 11:32:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862752907-4766185488: GET /api/scraping/sessions
2025-07-07 11:32:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862752907-4766185488: 200
2025-07-07 11:32:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862754950-4753405328: GET /api/scraping/sessions
2025-07-07 11:32:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862754950-4753405328: 200
2025-07-07 11:32:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862756998-4765889744: GET /api/scraping/sessions
2025-07-07 11:32:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862756998-4765889744: 200
2025-07-07 11:32:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862759038-4766177936: GET /api/scraping/sessions
2025-07-07 11:32:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862759038-4766177936: 200
2025-07-07 11:32:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862761082-4766171344: GET /api/scraping/sessions
2025-07-07 11:32:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862761082-4766171344: 200
2025-07-07 11:32:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862763128-4766171472: GET /api/scraping/sessions
2025-07-07 11:32:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862763128-4766171472: 200
2025-07-07 11:32:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862765177-4765957392: GET /api/scraping/sessions
2025-07-07 11:32:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862765177-4765957392: 200
2025-07-07 11:32:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862767222-4766179792: GET /api/scraping/sessions
2025-07-07 11:32:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862767222-4766179792: 200
2025-07-07 11:32:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862769259-4766177104: GET /api/scraping/sessions
2025-07-07 11:32:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862769259-4766177104: 200
2025-07-07 11:32:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862771302-4754232400: GET /api/scraping/sessions
2025-07-07 11:32:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862771302-4754232400: 200
2025-07-07 11:32:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862773353-4765954064: GET /api/scraping/sessions
2025-07-07 11:32:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862773353-4765954064: 200
2025-07-07 11:32:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862775398-4754218832: GET /api/scraping/sessions
2025-07-07 11:32:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862775398-4754218832: 200
2025-07-07 11:32:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751862777444-4766175312: GET /api/scraping/sessions
2025-07-07 11:32:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751862777444-4766175312: 200
2025-07-07 11:32:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862779492-4766171792: GET /api/scraping/sessions
2025-07-07 11:32:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862779492-4766171792: 200
2025-07-07 11:33:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862781540-4765957392: GET /api/scraping/sessions
2025-07-07 11:33:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862781540-4765957392: 200
2025-07-07 11:33:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862783589-4765990608: GET /api/scraping/sessions
2025-07-07 11:33:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862783589-4765990608: 200
2025-07-07 11:33:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862785635-4754218448: GET /api/scraping/sessions
2025-07-07 11:33:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862785635-4754218448: 200
2025-07-07 11:33:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862787675-4754196496: GET /api/scraping/sessions
2025-07-07 11:33:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862787675-4754196496: 200
2025-07-07 11:33:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862789715-4765976016: GET /api/scraping/sessions
2025-07-07 11:33:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862789715-4765976016: 200
2025-07-07 11:33:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862791769-4766178960: GET /api/scraping/sessions
2025-07-07 11:33:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862791769-4766178960: 200
2025-07-07 11:33:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862793817-4765990096: GET /api/scraping/sessions
2025-07-07 11:33:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862793817-4765990096: 200
2025-07-07 11:33:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862795865-4765784016: GET /api/scraping/sessions
2025-07-07 11:33:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862795865-4765784016: 200
2025-07-07 11:33:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862797911-4748121808: GET /api/scraping/sessions
2025-07-07 11:33:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862797911-4748121808: 200
2025-07-07 11:33:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862799953-4766172112: GET /api/scraping/sessions
2025-07-07 11:33:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862799953-4766172112: 200
2025-07-07 11:33:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862802001-4766179088: GET /api/scraping/sessions
2025-07-07 11:33:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862802001-4766179088: 200
2025-07-07 11:33:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862804047-4765781200: GET /api/scraping/sessions
2025-07-07 11:33:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862804047-4765781200: 200
2025-07-07 11:33:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862806095-4765955792: GET /api/scraping/sessions
2025-07-07 11:33:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862806095-4765955792: 200
2025-07-07 11:33:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862808143-4766183056: GET /api/scraping/sessions
2025-07-07 11:33:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862808143-4766183056: 200
2025-07-07 11:33:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862810189-4766181584: GET /api/scraping/sessions
2025-07-07 11:33:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862810189-4766181584: 200
2025-07-07 11:33:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862812629-4766180624: GET /api/scraping/sessions
2025-07-07 11:33:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862812629-4766180624: 200
2025-07-07 11:33:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862814677-4766175888: GET /api/scraping/sessions
2025-07-07 11:33:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862814677-4766175888: 200
2025-07-07 11:33:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862816728-4765956176: GET /api/scraping/sessions
2025-07-07 11:33:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862816728-4765956176: 200
2025-07-07 11:33:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862818778-4753398672: GET /api/scraping/sessions
2025-07-07 11:33:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862818778-4753398672: 200
2025-07-07 11:33:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751862820825-4766172240: GET /api/scraping/sessions
2025-07-07 11:33:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862820825-4766172240: 200
2025-07-07 11:33:42 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:33:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751862822869-4766183312: GET /api/scraping/sessions
2025-07-07 11:33:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751862822869-4766183312: 200
2025-07-07 11:33:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862824912-4765785040: GET /api/scraping/sessions
2025-07-07 11:33:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862824912-4765785040: 200
2025-07-07 11:33:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862826956-4754230928: GET /api/scraping/sessions
2025-07-07 11:33:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862826956-4754230928: 200
2025-07-07 11:33:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862829004-4753462608: GET /api/scraping/sessions
2025-07-07 11:33:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862829004-4753462608: 200
2025-07-07 11:33:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862831053-4765783952: GET /api/scraping/sessions
2025-07-07 11:33:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862831053-4765783952: 200
2025-07-07 11:33:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862833103-4754216848: GET /api/scraping/sessions
2025-07-07 11:33:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862833103-4754216848: 200
2025-07-07 11:33:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862835147-4765887376: GET /api/scraping/sessions
2025-07-07 11:33:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862835147-4765887376: 200
2025-07-07 11:33:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751862837184-4765984336: GET /api/scraping/sessions
2025-07-07 11:33:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751862837184-4765984336: 200
2025-07-07 11:33:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862839230-4765943248: GET /api/scraping/sessions
2025-07-07 11:33:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862839230-4765943248: 200
2025-07-07 11:34:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862841275-4766185616: GET /api/scraping/sessions
2025-07-07 11:34:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862841275-4766185616: 200
2025-07-07 11:34:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862843321-4766173200: GET /api/scraping/sessions
2025-07-07 11:34:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862843321-4766173200: 200
2025-07-07 11:34:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862845357-4765792400: GET /api/scraping/sessions
2025-07-07 11:34:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862845357-4765792400: 200
2025-07-07 11:34:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862847399-4754232336: GET /api/scraping/sessions
2025-07-07 11:34:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862847399-4754232336: 200
2025-07-07 11:34:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862849443-4765946768: GET /api/scraping/sessions
2025-07-07 11:34:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862849443-4765946768: 200
2025-07-07 11:34:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862851489-4766179216: GET /api/scraping/sessions
2025-07-07 11:34:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862851489-4766179216: 200
2025-07-07 11:34:13 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:34:13 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:34:13 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:34:14 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:34:14 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:34:14 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:34:14 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:34:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862854541-4430765520: GET /api/scraping/sessions
2025-07-07 11:34:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862854541-4430765520: 200
2025-07-07 11:34:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862856573-4431403024: GET /api/scraping/sessions
2025-07-07 11:34:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862856573-4431403024: 200
2025-07-07 11:34:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862858608-4430773648: GET /api/scraping/sessions
2025-07-07 11:34:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862858608-4430773648: 200
2025-07-07 11:34:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862860653-4431452368: GET /api/scraping/sessions
2025-07-07 11:34:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862860653-4431452368: 200
2025-07-07 11:34:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862862692-4431412816: GET /api/scraping/sessions
2025-07-07 11:34:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862862692-4431412816: 200
2025-07-07 11:34:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862864729-4431450960: GET /api/scraping/sessions
2025-07-07 11:34:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862864729-4431450960: 200
2025-07-07 11:34:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862866776-4431400400: GET /api/scraping/sessions
2025-07-07 11:34:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862866776-4431400400: 200
2025-07-07 11:34:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862868818-4431455248: GET /api/scraping/sessions
2025-07-07 11:34:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862868818-4431455248: 200
2025-07-07 11:34:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862870862-4431402896: GET /api/scraping/sessions
2025-07-07 11:34:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862870862-4431402896: 200
2025-07-07 11:34:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862872904-4431461328: GET /api/scraping/sessions
2025-07-07 11:34:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862872904-4431461328: 200
2025-07-07 11:34:33 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:34:33 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:34:33 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:34:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:34:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:34:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:34:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:34:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862874956-4467515600: GET /api/scraping/sessions
2025-07-07 11:34:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862874956-4467515600: 200
2025-07-07 11:34:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862876999-4468135888: GET /api/scraping/sessions
2025-07-07 11:34:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862876999-4468135888: 200
2025-07-07 11:34:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862879036-4466296208: GET /api/scraping/sessions
2025-07-07 11:34:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862879036-4466296208: 200
2025-07-07 11:34:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751862881083-4468185616: GET /api/scraping/sessions
2025-07-07 11:34:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751862881083-4468185616: 200
2025-07-07 11:34:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751862883127-4468137680: GET /api/scraping/sessions
2025-07-07 11:34:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751862883127-4468137680: 200
2025-07-07 11:34:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751862885164-4468179856: GET /api/scraping/sessions
2025-07-07 11:34:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751862885164-4468179856: 200
2025-07-07 11:34:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751862887207-4468194960: GET /api/scraping/sessions
2025-07-07 11:34:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751862887207-4468194960: 200
2025-07-07 11:34:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751862889252-4468135440: GET /api/scraping/sessions
2025-07-07 11:34:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751862889252-4468135440: 200
2025-07-07 11:34:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751862891297-4468137680: GET /api/scraping/sessions
2025-07-07 11:34:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751862891297-4468137680: 200
2025-07-07 11:34:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751862893340-4468194960: GET /api/scraping/sessions
2025-07-07 11:34:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751862893340-4468194960: 200
2025-07-07 11:34:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751862895384-4468182544: GET /api/scraping/sessions
2025-07-07 11:34:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751862895384-4468182544: 200
2025-07-07 11:34:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751862897429-4468145872: GET /api/scraping/sessions
2025-07-07 11:34:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751862897429-4468145872: 200
2025-07-07 11:34:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751862899475-4468183504: GET /api/scraping/sessions
2025-07-07 11:34:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862899475-4468183504: 200
2025-07-07 11:35:00 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:35:00 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:35:00 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:35:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:35:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:35:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:35:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:35:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862901772-4425119312: GET /api/scraping/sessions
2025-07-07 11:35:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862901772-4425119312: 200
2025-07-07 11:35:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862903807-4425815632: GET /api/scraping/sessions
2025-07-07 11:35:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862903807-4425815632: 200
2025-07-07 11:35:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862905850-4425836368: GET /api/scraping/sessions
2025-07-07 11:35:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862905850-4425836368: 200
2025-07-07 11:35:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862907889-4425229520: GET /api/scraping/sessions
2025-07-07 11:35:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862907889-4425229520: 200
2025-07-07 11:35:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862909933-4425875984: GET /api/scraping/sessions
2025-07-07 11:35:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862909933-4425875984: 200
2025-07-07 11:35:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862911981-4425837392: GET /api/scraping/sessions
2025-07-07 11:35:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862911981-4425837392: 200
2025-07-07 11:35:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751862914026-4425876624: GET /api/scraping/sessions
2025-07-07 11:35:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751862914026-4425876624: 200
2025-07-07 11:35:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751862916071-4425228048: GET /api/scraping/sessions
2025-07-07 11:35:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751862916071-4425228048: 200
2025-07-07 11:35:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751862918117-4425877456: GET /api/scraping/sessions
2025-07-07 11:35:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751862918117-4425877456: 200
2025-07-07 11:35:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751862920162-4423040976: GET /api/scraping/sessions
2025-07-07 11:35:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751862920162-4423040976: 200
2025-07-07 11:35:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751862922205-4425875664: GET /api/scraping/sessions
2025-07-07 11:35:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751862922205-4425875664: 200
2025-07-07 11:35:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751862924247-4425840144: GET /api/scraping/sessions
2025-07-07 11:35:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751862924247-4425840144: 200
2025-07-07 11:35:24 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:35:24 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:35:24 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:35:25 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:35:25 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:35:25 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:35:25 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:35:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751862926296-4565729744: GET /api/scraping/sessions
2025-07-07 11:35:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751862926296-4565729744: 200
2025-07-07 11:35:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751862928348-4566364560: GET /api/scraping/sessions
2025-07-07 11:35:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751862928348-4566364560: 200
2025-07-07 11:35:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751862930391-4566395024: GET /api/scraping/sessions
2025-07-07 11:35:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751862930391-4566395024: 200
2025-07-07 11:35:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751862932434-4565736592: GET /api/scraping/sessions
2025-07-07 11:35:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751862932434-4565736592: 200
2025-07-07 11:35:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751862934470-4566418128: GET /api/scraping/sessions
2025-07-07 11:35:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751862934470-4566418128: 200
2025-07-07 11:35:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751862936504-4565630480: GET /api/scraping/sessions
2025-07-07 11:35:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751862936504-4565630480: 200
2025-07-07 11:35:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751862938548-4566420048: GET /api/scraping/sessions
2025-07-07 11:35:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751862938548-4566420048: 200
2025-07-07 11:35:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751862940593-4427120272: GET /api/scraping/sessions
2025-07-07 11:35:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751862940593-4427120272: 200
2025-07-07 11:35:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751862942636-4566424016: GET /api/scraping/sessions
2025-07-07 11:35:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751862942636-4566424016: 200
2025-07-07 11:35:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751862944681-4565630928: GET /api/scraping/sessions
2025-07-07 11:35:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751862944681-4565630928: 200
2025-07-07 11:35:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751862946725-4566426576: GET /api/scraping/sessions
2025-07-07 11:35:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751862946725-4566426576: 200
2025-07-07 11:35:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751862948771-4564465104: GET /api/scraping/sessions
2025-07-07 11:35:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751862948771-4564465104: 200
2025-07-07 11:35:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751862950816-4566423440: GET /api/scraping/sessions
2025-07-07 11:35:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751862950816-4566423440: 200
2025-07-07 11:35:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751862952858-4566362768: GET /api/scraping/sessions
2025-07-07 11:35:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751862952858-4566362768: 200
2025-07-07 11:35:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751862954902-4566431056: GET /api/scraping/sessions
2025-07-07 11:35:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751862954902-4566431056: 200
2025-07-07 11:35:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751862956947-4566393424: GET /api/scraping/sessions
2025-07-07 11:35:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751862956947-4566393424: 200
2025-07-07 11:35:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751862958992-4566430672: GET /api/scraping/sessions
2025-07-07 11:35:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751862958992-4566430672: 200
2025-07-07 11:36:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751862961035-4565733520: GET /api/scraping/sessions
2025-07-07 11:36:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751862961035-4565733520: 200
2025-07-07 11:36:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751862963082-4566420944: GET /api/scraping/sessions
2025-07-07 11:36:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751862963082-4566420944: 200
2025-07-07 11:36:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751862965130-4566395280: GET /api/scraping/sessions
2025-07-07 11:36:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751862965130-4566395280: 200
2025-07-07 11:36:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751862967174-4566420368: GET /api/scraping/sessions
2025-07-07 11:36:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751862967174-4566420368: 200
2025-07-07 11:36:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751862969218-4566394192: GET /api/scraping/sessions
2025-07-07 11:36:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751862969218-4566394192: 200
2025-07-07 11:36:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751862971261-4566417872: GET /api/scraping/sessions
2025-07-07 11:36:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751862971261-4566417872: 200
2025-07-07 11:36:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751862973305-4565738320: GET /api/scraping/sessions
2025-07-07 11:36:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751862973305-4565738320: 200
2025-07-07 11:36:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751862975351-4566419984: GET /api/scraping/sessions
2025-07-07 11:36:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751862975351-4566419984: 200
2025-07-07 11:36:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751862977394-4566391248: GET /api/scraping/sessions
2025-07-07 11:36:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751862977394-4566391248: 200
2025-07-07 11:36:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751862979430-4566425808: GET /api/scraping/sessions
2025-07-07 11:36:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751862979430-4566425808: 200
2025-07-07 11:36:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751862981472-4565731216: GET /api/scraping/sessions
2025-07-07 11:36:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751862981472-4565731216: 200
2025-07-07 11:36:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751862983516-4566433488: GET /api/scraping/sessions
2025-07-07 11:36:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751862983516-4566433488: 200
2025-07-07 11:36:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751862985557-4566400464: GET /api/scraping/sessions
2025-07-07 11:36:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751862985557-4566400464: 200
2025-07-07 11:36:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751862987598-4566427984: GET /api/scraping/sessions
2025-07-07 11:36:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751862987598-4566427984: 200
2025-07-07 11:36:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751862989646-4566432720: GET /api/scraping/sessions
2025-07-07 11:36:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751862989646-4566432720: 200
2025-07-07 11:36:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751862991693-4566431056: GET /api/scraping/sessions
2025-07-07 11:36:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751862991693-4566431056: 200
2025-07-07 11:36:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751862993737-4566428176: GET /api/scraping/sessions
2025-07-07 11:36:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751862993737-4566428176: 200
2025-07-07 11:36:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751862995779-4566395216: GET /api/scraping/sessions
2025-07-07 11:36:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751862995779-4566395216: 200
2025-07-07 11:36:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751862997821-4566431824: GET /api/scraping/sessions
2025-07-07 11:36:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751862997821-4566431824: 200
2025-07-07 11:36:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751862999866-4566398032: GET /api/scraping/sessions
2025-07-07 11:36:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751862999866-4566398032: 200
2025-07-07 11:36:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863001911-4566385744: GET /api/scraping/sessions
2025-07-07 11:36:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863001911-4566385744: 200
2025-07-07 11:36:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863003955-4566418512: GET /api/scraping/sessions
2025-07-07 11:36:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863003955-4566418512: 200
2025-07-07 11:36:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863005998-4566433744: GET /api/scraping/sessions
2025-07-07 11:36:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863005998-4566433744: 200
2025-07-07 11:36:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863008044-4566386192: GET /api/scraping/sessions
2025-07-07 11:36:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863008044-4566386192: 200
2025-07-07 11:36:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863010088-4566394832: GET /api/scraping/sessions
2025-07-07 11:36:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863010088-4566394832: 200
2025-07-07 11:36:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863012130-4566421648: GET /api/scraping/sessions
2025-07-07 11:36:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863012130-4566421648: 200
2025-07-07 11:36:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863014174-4566426960: GET /api/scraping/sessions
2025-07-07 11:36:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863014174-4566426960: 200
2025-07-07 11:36:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863016221-4566397008: GET /api/scraping/sessions
2025-07-07 11:36:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863016221-4566397008: 200
2025-07-07 11:36:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863018262-4566392976: GET /api/scraping/sessions
2025-07-07 11:36:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863018262-4566392976: 200
2025-07-07 11:37:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863020305-4566420816: GET /api/scraping/sessions
2025-07-07 11:37:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863020305-4566420816: 200
2025-07-07 11:37:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863022341-4566421264: GET /api/scraping/sessions
2025-07-07 11:37:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863022341-4566421264: 200
2025-07-07 11:37:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863024385-4566393808: GET /api/scraping/sessions
2025-07-07 11:37:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863024385-4566393808: 200
2025-07-07 11:37:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863026427-4566393872: GET /api/scraping/sessions
2025-07-07 11:37:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863026427-4566393872: 200
2025-07-07 11:37:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863028469-4566426768: GET /api/scraping/sessions
2025-07-07 11:37:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863028469-4566426768: 200
2025-07-07 11:37:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863030510-4566419472: GET /api/scraping/sessions
2025-07-07 11:37:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863030510-4566419472: 200
2025-07-07 11:37:10 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:37:10 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:37:10 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:37:11 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:37:11 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:37:11 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:37:11 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:37:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863032564-4427530704: GET /api/scraping/sessions
2025-07-07 11:37:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863032564-4427530704: 200
2025-07-07 11:37:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863034616-4428118032: GET /api/scraping/sessions
2025-07-07 11:37:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863034616-4428118032: 200
2025-07-07 11:37:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863036664-4428147856: GET /api/scraping/sessions
2025-07-07 11:37:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863036664-4428147856: 200
2025-07-07 11:37:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863038705-4428118864: GET /api/scraping/sessions
2025-07-07 11:37:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863038705-4428118864: 200
2025-07-07 11:37:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863040749-4428204432: GET /api/scraping/sessions
2025-07-07 11:37:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863040749-4428204432: 200
2025-07-07 11:37:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863042789-4428152208: GET /api/scraping/sessions
2025-07-07 11:37:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863042789-4428152208: 200
2025-07-07 11:37:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863044832-4428210128: GET /api/scraping/sessions
2025-07-07 11:37:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863044832-4428210128: 200
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045913-4427533008: GET /api/scraping/test-zendriver-service
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045913-4427533008: 200
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045923-4428203600: POST /api/scraping/sessions
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045923-4428203600: 201
2025-07-07 11:37:25 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:37:25 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:37:25 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045943-4428207184: GET /api/scraping/sessions/25
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045943-4428207184: 200
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045949-4428316304: GET /api/scraping/sessions/25
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045949-4428316304: 200
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045956-4428147920: GET /api/scraping/sessions/25/export
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045956-4428147920: 404
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045965-4447050384: GET /api/scraping/sessions/25/export
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045965-4447050384: 404
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863045972-4428313488: POST /api/scraping/sessions/25/start
2025-07-07 11:37:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863045972-4428313488: 200
2025-07-07 11:37:25 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:37:25 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:37:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863046866-4447055120: GET /api/scraping/sessions
2025-07-07 11:37:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863046866-4447055120: 200
2025-07-07 11:37:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863048911-4428138768: GET /api/scraping/sessions
2025-07-07 11:37:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863048911-4428138768: 200
2025-07-07 11:37:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863050959-4447012816: GET /api/scraping/sessions
2025-07-07 11:37:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863050959-4447012816: 200
2025-07-07 11:37:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863053004-4447097168: GET /api/scraping/sessions
2025-07-07 11:37:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863053004-4447097168: 200
2025-07-07 11:37:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863055051-4447103312: GET /api/scraping/sessions
2025-07-07 11:37:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863055051-4447103312: 200
2025-07-07 11:37:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863057095-4428213968: GET /api/scraping/sessions
2025-07-07 11:37:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863057095-4428213968: 200
2025-07-07 11:37:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863059145-4447012432: GET /api/scraping/sessions
2025-07-07 11:37:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863059145-4447012432: 200
2025-07-07 11:37:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863061185-4447106192: GET /api/scraping/sessions
2025-07-07 11:37:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863061185-4447106192: 200
2025-07-07 11:37:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863063219-4447103504: GET /api/scraping/sessions
2025-07-07 11:37:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863063219-4447103504: 200
2025-07-07 11:37:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863065264-4447097936: GET /api/scraping/sessions
2025-07-07 11:37:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863065264-4447097936: 200
2025-07-07 11:37:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863067313-4428215056: GET /api/scraping/sessions
2025-07-07 11:37:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863067313-4428215056: 200
2025-07-07 11:37:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751863069358-4447100048: GET /api/scraping/sessions
2025-07-07 11:37:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751863069358-4447100048: 200
2025-07-07 11:37:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863071412-**********: GET /api/scraping/sessions
2025-07-07 11:37:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863071412-**********: 200
2025-07-07 11:37:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863073454-**********: GET /api/scraping/sessions
2025-07-07 11:37:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863073454-**********: 200
2025-07-07 11:37:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863074427-**********: GET /health
2025-07-07 11:37:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863074427-**********: 200
2025-07-07 11:37:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863075499-**********: GET /api/scraping/sessions
2025-07-07 11:37:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863075499-**********: 200
2025-07-07 11:37:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863077539-**********: GET /api/scraping/sessions
2025-07-07 11:37:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863077539-**********: 200
2025-07-07 11:37:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863079586-**********: GET /api/scraping/sessions
2025-07-07 11:37:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863079586-**********: 200
2025-07-07 11:38:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863080064-**********: GET /api/scraping/sessions/25/export
2025-07-07 11:38:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863080064-**********: 404
2025-07-07 11:38:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863081634-4447081104: GET /api/scraping/sessions
2025-07-07 11:38:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863081634-4447081104: 200
2025-07-07 11:38:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863083681-4447011600: GET /api/scraping/sessions
2025-07-07 11:38:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863083681-4447011600: 200
2025-07-07 11:38:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863085725-4447107472: GET /api/scraping/sessions
2025-07-07 11:38:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863085725-4447107472: 200
2025-07-07 11:38:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863085785-4428314704: GET /docs
2025-07-07 11:38:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863085785-4428314704: 200
2025-07-07 11:38:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863087770-4447077392: GET /api/scraping/sessions
2025-07-07 11:38:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863087770-4447077392: 200
2025-07-07 11:38:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863089816-4447100752: GET /api/scraping/sessions
2025-07-07 11:38:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863089816-4447100752: 200
2025-07-07 11:38:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863091204-4447102352: GET /openapi.json
2025-07-07 11:38:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863091204-4447102352: 200
2025-07-07 11:38:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863091867-4447386896: GET /api/scraping/sessions
2025-07-07 11:38:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863091867-4447386896: 200
2025-07-07 11:38:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863093913-4447383376: GET /api/scraping/sessions
2025-07-07 11:38:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863093913-4447383376: 200
2025-07-07 11:38:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863095958-4447375824: GET /api/scraping/sessions
2025-07-07 11:38:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863095958-4447375824: 200
2025-07-07 11:38:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863098006-4447083280: GET /api/scraping/sessions
2025-07-07 11:38:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863098006-4447083280: 200
2025-07-07 11:38:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863100053-4447238608: GET /api/scraping/sessions
2025-07-07 11:38:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863100053-4447238608: 200
2025-07-07 11:38:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863102095-4447377552: GET /api/scraping/sessions
2025-07-07 11:38:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863102095-4447377552: 200
2025-07-07 11:38:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863102193-4428315280: GET /api/scraping/sessions/25
2025-07-07 11:38:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863102193-4428315280: 200
2025-07-07 11:38:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863104140-4447378320: GET /api/scraping/sessions
2025-07-07 11:38:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863104140-4447378320: 200
2025-07-07 11:38:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863106186-4447239440: GET /api/scraping/sessions
2025-07-07 11:38:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863106186-4447239440: 200
2025-07-07 11:38:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863107984-4447380880: POST /api/scraping/test-html-uid-extraction
2025-07-07 11:38:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863107984-4447380880: 200
2025-07-07 11:38:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863108228-4447076816: GET /api/scraping/sessions
2025-07-07 11:38:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863108228-4447076816: 200
2025-07-07 11:38:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863110271-4448118160: GET /api/scraping/sessions
2025-07-07 11:38:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863110271-4448118160: 200
2025-07-07 11:38:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863112318-4448111504: GET /api/scraping/sessions
2025-07-07 11:38:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863112318-4448111504: 200
2025-07-07 11:38:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863114363-4448091088: GET /api/scraping/sessions
2025-07-07 11:38:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863114363-4448091088: 200
2025-07-07 11:38:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863116408-4448114384: GET /api/scraping/sessions
2025-07-07 11:38:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863116408-4448114384: 200
2025-07-07 11:38:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863118452-4448085136: GET /api/scraping/sessions
2025-07-07 11:38:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863118452-4448085136: 200
2025-07-07 11:38:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863120499-4447239440: GET /api/scraping/sessions
2025-07-07 11:38:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863120499-4447239440: 200
2025-07-07 11:38:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863122543-4448079248: GET /api/scraping/sessions
2025-07-07 11:38:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863122543-4448079248: 200
2025-07-07 11:38:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863124591-4447107024: GET /api/scraping/sessions
2025-07-07 11:38:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863124591-4447107024: 200
2025-07-07 11:38:46 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:38:46 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:38:46 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:38:47 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:38:47 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:38:47 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:38:47 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:38:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863127366-4403839376: GET /api/scraping/sessions
2025-07-07 11:38:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863127366-4403839376: 200
2025-07-07 11:38:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751863129405-4404441104: GET /api/scraping/sessions
2025-07-07 11:38:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751863129405-4404441104: 200
2025-07-07 11:38:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863131450-4404454096: GET /api/scraping/sessions
2025-07-07 11:38:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863131450-4404454096: 200
2025-07-07 11:38:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863133493-4404442192: GET /api/scraping/sessions
2025-07-07 11:38:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863133493-4404442192: 200
2025-07-07 11:38:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863135540-4404495568: GET /api/scraping/sessions
2025-07-07 11:38:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863135540-4404495568: 200
2025-07-07 11:38:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863137584-4404455120: GET /api/scraping/sessions
2025-07-07 11:38:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863137584-4404455120: 200
2025-07-07 11:38:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863139629-4404504272: GET /api/scraping/sessions
2025-07-07 11:38:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863139629-4404504272: 200
2025-07-07 11:39:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863141677-4403840848: GET /api/scraping/sessions
2025-07-07 11:39:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863141677-4403840848: 200
2025-07-07 11:39:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863143724-4404497232: GET /api/scraping/sessions
2025-07-07 11:39:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863143724-4404497232: 200
2025-07-07 11:39:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863145771-4404459472: GET /api/scraping/sessions
2025-07-07 11:39:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863145771-4404459472: 200
2025-07-07 11:39:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863147818-4404494928: GET /api/scraping/sessions
2025-07-07 11:39:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863147818-4404494928: 200
2025-07-07 11:39:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863149861-4403839312: GET /api/scraping/sessions
2025-07-07 11:39:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863149861-4403839312: 200
2025-07-07 11:39:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863151909-4404496400: GET /api/scraping/sessions
2025-07-07 11:39:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863151909-4404496400: 200
2025-07-07 11:39:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863153953-4404460560: GET /api/scraping/sessions
2025-07-07 11:39:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863153953-4404460560: 200
2025-07-07 11:39:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863155997-4404496272: GET /api/scraping/sessions
2025-07-07 11:39:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863155997-4404496272: 200
2025-07-07 11:39:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863158043-4403626320: GET /api/scraping/sessions
2025-07-07 11:39:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863158043-4403626320: 200
2025-07-07 11:39:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863160082-4404509520: GET /api/scraping/sessions
2025-07-07 11:39:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863160082-4404509520: 200
2025-07-07 11:39:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863162127-4404498320: GET /api/scraping/sessions
2025-07-07 11:39:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863162127-4404498320: 200
2025-07-07 11:39:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863164173-4404431824: GET /api/scraping/sessions
2025-07-07 11:39:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863164173-4404431824: 200
2025-07-07 11:39:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863166217-4404458384: GET /api/scraping/sessions
2025-07-07 11:39:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863166217-4404458384: 200
2025-07-07 11:39:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863168262-4404505872: GET /api/scraping/sessions
2025-07-07 11:39:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863168262-4404505872: 200
2025-07-07 11:39:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863170307-4404497168: GET /api/scraping/sessions
2025-07-07 11:39:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863170307-4404497168: 200
2025-07-07 11:39:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863172350-4404444240: GET /api/scraping/sessions
2025-07-07 11:39:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863172350-4404444240: 200
2025-07-07 11:39:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863174396-4404454608: GET /api/scraping/sessions
2025-07-07 11:39:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863174396-4404454608: 200
2025-07-07 11:39:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863176443-4404507664: GET /api/scraping/sessions
2025-07-07 11:39:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863176443-4404507664: 200
2025-07-07 11:39:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863178490-4404441360: GET /api/scraping/sessions
2025-07-07 11:39:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863178490-4404441360: 200
2025-07-07 11:39:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863180527-4404509840: GET /api/scraping/sessions
2025-07-07 11:39:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863180527-4404509840: 200
2025-07-07 11:39:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863182571-4404450832: GET /api/scraping/sessions
2025-07-07 11:39:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863182571-4404450832: 200
2025-07-07 11:39:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863184615-4404510672: GET /api/scraping/sessions
2025-07-07 11:39:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863184615-4404510672: 200
2025-07-07 11:39:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863186660-4404445712: GET /api/scraping/sessions
2025-07-07 11:39:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863186660-4404445712: 200
2025-07-07 11:39:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863188707-4404496144: GET /api/scraping/sessions
2025-07-07 11:39:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863188707-4404496144: 200
2025-07-07 11:39:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863190756-4403841872: GET /api/scraping/sessions
2025-07-07 11:39:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863190756-4403841872: 200
2025-07-07 11:39:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863192805-4404442128: GET /api/scraping/sessions
2025-07-07 11:39:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863192805-4404442128: 200
2025-07-07 11:39:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863194855-4401673552: GET /api/scraping/sessions
2025-07-07 11:39:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863194855-4401673552: 200
2025-07-07 11:39:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863196911-4404449040: GET /api/scraping/sessions
2025-07-07 11:39:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863196911-4404449040: 200
2025-07-07 11:39:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863198959-4404508240: GET /api/scraping/sessions
2025-07-07 11:39:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863198959-4404508240: 200
2025-07-07 11:40:01 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:40:01 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:40:01 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:40:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:40:02 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:40:02 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:40:02 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:40:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863202016-4428545680: GET /api/scraping/sessions
2025-07-07 11:40:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863202016-4428545680: 200
2025-07-07 11:40:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863204054-4429262032: GET /api/scraping/sessions
2025-07-07 11:40:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863204054-4429262032: 200
2025-07-07 11:40:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863206097-4429273872: GET /api/scraping/sessions
2025-07-07 11:40:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863206097-4429273872: 200
2025-07-07 11:40:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863208144-4429273360: GET /api/scraping/sessions
2025-07-07 11:40:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863208144-4429273360: 200
2025-07-07 11:40:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863210190-4429334096: GET /api/scraping/sessions
2025-07-07 11:40:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863210190-4429334096: 200
2025-07-07 11:40:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863212236-4429268880: GET /api/scraping/sessions
2025-07-07 11:40:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863212236-4429268880: 200
2025-07-07 11:40:12 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:40:12 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:40:12 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:40:13 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:40:13 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:40:13 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:40:13 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:40:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863214305-4466047184: GET /api/scraping/sessions
2025-07-07 11:40:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863214305-4466047184: 200
2025-07-07 11:40:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863216359-4466709008: GET /api/scraping/sessions
2025-07-07 11:40:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863216359-4466709008: 200
2025-07-07 11:40:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863218406-4466742288: GET /api/scraping/sessions
2025-07-07 11:40:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863218406-4466742288: 200
2025-07-07 11:40:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863220453-4466131216: GET /api/scraping/sessions
2025-07-07 11:40:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863220453-4466131216: 200
2025-07-07 11:40:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:40:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:40:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:40:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:40:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:40:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:40:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:40:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863223355-4396874320: GET /api/scraping/sessions
2025-07-07 11:40:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863223355-4396874320: 200
2025-07-07 11:40:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863225391-4397573520: GET /api/scraping/sessions
2025-07-07 11:40:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863225391-4397573520: 200
2025-07-07 11:40:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863227438-4397586000: GET /api/scraping/sessions
2025-07-07 11:40:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863227438-4397586000: 200
2025-07-07 11:40:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863229484-4396727632: GET /api/scraping/sessions
2025-07-07 11:40:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863229484-4396727632: 200
2025-07-07 11:40:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751863231534-4397587984: GET /api/scraping/sessions
2025-07-07 11:40:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751863231534-4397587984: 200
2025-07-07 11:40:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863233585-4397592784: GET /api/scraping/sessions
2025-07-07 11:40:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863233585-4397592784: 200
2025-07-07 11:40:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863235632-4397629840: GET /api/scraping/sessions
2025-07-07 11:40:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863235632-4397629840: 200
2025-07-07 11:40:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863235779-4396975120: GET /api/scraping/sessions/27/export
2025-07-07 11:40:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863235779-4396975120: 500
2025-07-07 11:40:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863237677-4397716624: GET /api/scraping/sessions
2025-07-07 11:40:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863237677-4397716624: 200
2025-07-07 11:40:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863239726-4397638160: GET /api/scraping/sessions
2025-07-07 11:40:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863239726-4397638160: 200
2025-07-07 11:40:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863241778-4397716176: GET /api/scraping/sessions
2025-07-07 11:40:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863241778-4397716176: 200
2025-07-07 11:40:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863243829-4397727632: GET /api/scraping/sessions
2025-07-07 11:40:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863243829-4397727632: 200
2025-07-07 11:40:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863245874-4397714448: GET /api/scraping/sessions
2025-07-07 11:40:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863245874-4397714448: 200
2025-07-07 11:40:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863247916-4396868624: GET /api/scraping/sessions
2025-07-07 11:40:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863247916-4396868624: 200
2025-07-07 11:40:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751863249965-4397714320: GET /api/scraping/sessions
2025-07-07 11:40:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751863249965-4397714320: 200
2025-07-07 11:40:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863252015-4397723344: GET /api/scraping/sessions
2025-07-07 11:40:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863252015-4397723344: 200
2025-07-07 11:40:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863254060-4397591120: GET /api/scraping/sessions
2025-07-07 11:40:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863254060-4397591120: 200
2025-07-07 11:40:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863256110-4397633040: GET /api/scraping/sessions
2025-07-07 11:40:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863256110-4397633040: 200
2025-07-07 11:40:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863258148-4397724112: GET /api/scraping/sessions
2025-07-07 11:40:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863258148-4397724112: 200
2025-07-07 11:40:59 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:40:59 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:40:59 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:41:00 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:41:00 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:41:00 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:41:00 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:41:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863260463-4428463888: GET /api/scraping/sessions
2025-07-07 11:41:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863260463-4428463888: 200
2025-07-07 11:41:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863262500-4429147600: GET /api/scraping/sessions
2025-07-07 11:41:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863262500-4429147600: 200
2025-07-07 11:41:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863264550-4429200464: GET /api/scraping/sessions
2025-07-07 11:41:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863264550-4429200464: 200
2025-07-07 11:41:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863266602-4429195792: GET /api/scraping/sessions
2025-07-07 11:41:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863266602-4429195792: 200
2025-07-07 11:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863268213-4429252304: GET /api/scraping/sessions/27/export
2025-07-07 11:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863268213-4429252304: 200
2025-07-07 11:41:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863268651-4429254544: GET /api/scraping/sessions
2025-07-07 11:41:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863268651-4429254544: 200
2025-07-07 11:41:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863270702-4429342096: GET /api/scraping/sessions
2025-07-07 11:41:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863270702-4429342096: 200
2025-07-07 11:41:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863272753-4429348176: GET /api/scraping/sessions
2025-07-07 11:41:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863272753-4429348176: 200
2025-07-07 11:41:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863274295-4429369104: GET /api/scraping/sessions/27/export
2025-07-07 11:41:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863282346-4429343440: GET /api/scraping/sessions
2025-07-07 11:41:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863274295-4429369104: 200
2025-07-07 11:41:22 | WARNING | middleware.performance:dispatch:64 | Slow request: GET /api/scraping/sessions/27/export took 8.054s
2025-07-07 11:41:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863282346-4429343440: 200
2025-07-07 11:41:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863284367-4429366736: GET /api/scraping/sessions
2025-07-07 11:41:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863284367-4429366736: 200
2025-07-07 11:41:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863286416-4524934224: GET /api/scraping/sessions
2025-07-07 11:41:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863286416-4524934224: 200
2025-07-07 11:41:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863287619-4524867216: GET /api/scraping/sessions/27/export
2025-07-07 11:41:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863287619-4524867216: 200
2025-07-07 11:41:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863288466-4429343248: GET /api/scraping/sessions
2025-07-07 11:41:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863288466-4429343248: 200
2025-07-07 11:41:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863290514-4524945296: GET /api/scraping/sessions
2025-07-07 11:41:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863290514-4524945296: 200
2025-07-07 11:41:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863292564-4524943248: GET /api/scraping/sessions
2025-07-07 11:41:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863292564-4524943248: 200
2025-07-07 11:41:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863294612-4429376208: GET /api/scraping/sessions
2025-07-07 11:41:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863294612-4429376208: 200
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295247-4429190608: GET /api/scraping/test-zendriver-service
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295247-4429190608: 200
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295256-4524866320: POST /api/scraping/sessions
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295256-4524866320: 201
2025-07-07 11:41:35 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:41:35 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:41:35 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295277-4429367760: GET /api/scraping/sessions/28
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295277-4429367760: 200
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295283-4524974224: GET /api/scraping/sessions/28
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295283-4524974224: 200
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295290-4478986512: GET /api/scraping/sessions/28/export
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295290-4478986512: 404
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295297-4524808592: GET /api/scraping/sessions/28/export
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295297-4524808592: 404
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863295304-4429370256: POST /api/scraping/sessions/28/start
2025-07-07 11:41:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863295304-4429370256: 200
2025-07-07 11:41:35 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:41:35 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:41:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863296656-4504645712: GET /api/scraping/sessions
2025-07-07 11:41:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863296656-4504645712: 200
2025-07-07 11:41:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863298706-4524810960: GET /api/scraping/sessions
2025-07-07 11:41:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863298706-4524810960: 200
2025-07-07 11:41:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863300759-4429375184: GET /api/scraping/sessions
2025-07-07 11:41:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863300759-4429375184: 200
2025-07-07 11:41:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863302812-4524812880: GET /api/scraping/sessions
2025-07-07 11:41:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863302812-4524812880: 200
2025-07-07 11:41:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863304860-4524807824: GET /api/scraping/sessions
2025-07-07 11:41:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863304860-4524807824: 200
2025-07-07 11:41:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863306907-4524935824: GET /api/scraping/sessions
2025-07-07 11:41:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863306907-4524935824: 200
2025-07-07 11:41:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863308957-4524809872: GET /api/scraping/sessions
2025-07-07 11:41:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863308957-4524809872: 200
2025-07-07 11:41:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863311007-4524857104: GET /api/scraping/sessions
2025-07-07 11:41:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863311007-4524857104: 200
2025-07-07 11:41:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863313058-4429376272: GET /api/scraping/sessions
2025-07-07 11:41:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863313058-4429376272: 200
2025-07-07 11:41:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863315107-4429339920: GET /api/scraping/sessions
2025-07-07 11:41:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863315107-4429339920: 200
2025-07-07 11:41:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863317156-4524813136: GET /api/scraping/sessions
2025-07-07 11:41:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863317156-4524813136: 200
2025-07-07 11:41:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863319205-4429376400: GET /api/scraping/sessions
2025-07-07 11:41:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863319205-4429376400: 200
2025-07-07 11:42:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863321257-4429369936: GET /api/scraping/sessions
2025-07-07 11:42:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863321257-4429369936: 200
2025-07-07 11:42:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863323310-4524812944: GET /api/scraping/sessions
2025-07-07 11:42:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863323310-4524812944: 200
2025-07-07 11:42:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863325357-4524862608: GET /api/scraping/sessions
2025-07-07 11:42:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863325357-4524862608: 200
2025-07-07 11:42:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863327403-4524967696: GET /api/scraping/sessions
2025-07-07 11:42:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863327403-4524967696: 200
2025-07-07 11:42:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863329452-4429348112: GET /api/scraping/sessions
2025-07-07 11:42:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863329452-4429348112: 200
2025-07-07 11:42:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863331502-4524857104: GET /api/scraping/sessions
2025-07-07 11:42:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863331502-4524857104: 200
2025-07-07 11:42:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863333551-4524757008: GET /api/scraping/sessions
2025-07-07 11:42:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863333551-4524757008: 200
2025-07-07 11:42:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863335600-4524866320: GET /api/scraping/sessions
2025-07-07 11:42:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863335600-4524866320: 200
2025-07-07 11:42:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863337650-4524977808: GET /api/scraping/sessions
2025-07-07 11:42:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863337650-4524977808: 200
2025-07-07 11:42:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751863339699-4512872400: GET /api/scraping/sessions
2025-07-07 11:42:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751863339699-4512872400: 200
2025-07-07 11:42:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751863341750-4524966800: GET /api/scraping/sessions
2025-07-07 11:42:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751863341750-4524966800: 200
2025-07-07 11:42:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863343803-4524969680: GET /api/scraping/sessions
2025-07-07 11:42:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863343803-4524969680: 200
2025-07-07 11:42:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863345853-4524859472: GET /api/scraping/sessions
2025-07-07 11:42:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863345853-4524859472: 200
2025-07-07 11:42:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863347903-4517694992: GET /api/scraping/sessions
2025-07-07 11:42:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863347903-4517694992: 200
2025-07-07 11:42:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863349952-4524977936: GET /api/scraping/sessions
2025-07-07 11:42:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863349952-4524977936: 200
2025-07-07 11:42:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863352004-4524814032: GET /api/scraping/sessions
2025-07-07 11:42:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863352004-4524814032: 200
2025-07-07 11:42:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863354059-4524972240: GET /api/scraping/sessions
2025-07-07 11:42:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863354059-4524972240: 200
2025-07-07 11:42:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863356108-4524810192: GET /api/scraping/sessions
2025-07-07 11:42:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863356108-4524810192: 200
2025-07-07 11:42:36 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:42:36 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:42:36 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:42:37 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:42:37 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:42:37 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:42:37 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:42:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863358178-4476532368: GET /api/scraping/sessions
2025-07-07 11:42:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863358178-4476532368: 200
2025-07-07 11:42:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863360251-4477204752: GET /api/scraping/sessions
2025-07-07 11:42:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863360251-4477204752: 200
2025-07-07 11:42:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863362319-4477254096: GET /api/scraping/sessions
2025-07-07 11:42:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863362319-4477254096: 200
2025-07-07 11:42:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863364420-4477240272: GET /api/scraping/sessions
2025-07-07 11:42:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863364420-4477240272: 200
2025-07-07 11:42:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863366473-4477290896: GET /api/scraping/sessions
2025-07-07 11:42:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863366473-4477290896: 200
2025-07-07 11:42:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863368523-4476623120: GET /api/scraping/sessions
2025-07-07 11:42:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863368523-4476623120: 200
2025-07-07 11:42:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863370570-4477293072: GET /api/scraping/sessions
2025-07-07 11:42:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863370570-4477293072: 200
2025-07-07 11:42:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863372613-4468513232: GET /api/scraping/sessions
2025-07-07 11:42:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863372613-4468513232: 200
2025-07-07 11:42:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863374660-4477290064: GET /api/scraping/sessions
2025-07-07 11:42:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863374660-4477290064: 200
2025-07-07 11:42:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863376712-4477250576: GET /api/scraping/sessions
2025-07-07 11:42:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863376712-4477250576: 200
2025-07-07 11:42:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863378764-4477300560: GET /api/scraping/sessions
2025-07-07 11:42:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863378764-4477300560: 200
2025-07-07 11:43:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863380817-4476531280: GET /api/scraping/sessions
2025-07-07 11:43:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863380817-4476531280: 200
2025-07-07 11:43:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863382868-4477299408: GET /api/scraping/sessions
2025-07-07 11:43:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863382868-4477299408: 200
2025-07-07 11:43:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863384914-4477240080: GET /api/scraping/sessions
2025-07-07 11:43:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863384914-4477240080: 200
2025-07-07 11:43:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863386956-4477303120: GET /api/scraping/sessions
2025-07-07 11:43:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863386956-4477303120: 200
2025-07-07 11:43:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863389005-4477203216: GET /api/scraping/sessions
2025-07-07 11:43:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863389005-4477203216: 200
2025-07-07 11:43:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863391055-4477292176: GET /api/scraping/sessions
2025-07-07 11:43:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863391055-4477292176: 200
2025-07-07 11:43:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863393105-4477248848: GET /api/scraping/sessions
2025-07-07 11:43:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863393105-4477248848: 200
2025-07-07 11:43:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863395158-4477303376: GET /api/scraping/sessions
2025-07-07 11:43:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863395158-4477303376: 200
2025-07-07 11:43:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863397210-4476534224: GET /api/scraping/sessions
2025-07-07 11:43:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863397210-4476534224: 200
2025-07-07 11:43:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751863399258-4477297424: GET /api/scraping/sessions
2025-07-07 11:43:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751863399258-4477297424: 200
2025-07-07 11:43:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751863401297-4476620368: GET /api/scraping/sessions
2025-07-07 11:43:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751863401297-4476620368: 200
2025-07-07 11:43:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863403348-4477302288: GET /api/scraping/sessions
2025-07-07 11:43:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863403348-4477302288: 200
2025-07-07 11:43:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863405400-4477289616: GET /api/scraping/sessions
2025-07-07 11:43:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863405400-4477289616: 200
2025-07-07 11:43:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863407452-4477248528: GET /api/scraping/sessions
2025-07-07 11:43:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863407452-4477248528: 200
2025-07-07 11:43:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863409508-4477200464: GET /api/scraping/sessions
2025-07-07 11:43:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863409508-4477200464: 200
2025-07-07 11:43:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751863411563-4477297232: GET /api/scraping/sessions
2025-07-07 11:43:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751863411563-4477297232: 200
2025-07-07 11:43:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863413610-4477254928: GET /api/scraping/sessions
2025-07-07 11:43:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863413610-4477254928: 200
2025-07-07 11:43:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863415657-4477304272: GET /api/scraping/sessions
2025-07-07 11:43:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863415657-4477304272: 200
2025-07-07 11:43:36 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:43:36 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:43:36 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:43:37 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:43:37 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:43:37 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:43:37 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:43:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863417720-4569841168: GET /api/scraping/sessions
2025-07-07 11:43:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863417720-4569841168: 200
2025-07-07 11:43:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863419770-4570539664: GET /api/scraping/sessions
2025-07-07 11:43:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863419770-4570539664: 200
2025-07-07 11:43:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863421812-4570577296: GET /api/scraping/sessions
2025-07-07 11:43:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863421812-4570577296: 200
2025-07-07 11:43:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863423860-4570575952: GET /api/scraping/sessions
2025-07-07 11:43:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863423860-4570575952: 200
2025-07-07 11:43:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863425914-4570536592: GET /api/scraping/sessions
2025-07-07 11:43:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863425914-4570536592: 200
2025-07-07 11:43:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863427967-4569839568: GET /api/scraping/sessions
2025-07-07 11:43:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863427967-4569839568: 200
2025-07-07 11:43:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863430019-4570612048: GET /api/scraping/sessions
2025-07-07 11:43:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863430019-4570612048: 200
2025-07-07 11:43:51 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:43:51 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:43:51 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:43:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:43:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:43:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:43:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:43:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863432091-4487019792: GET /api/scraping/sessions
2025-07-07 11:43:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863432091-4487019792: 200
2025-07-07 11:43:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863434128-4487707920: GET /api/scraping/sessions
2025-07-07 11:43:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863434128-4487707920: 200
2025-07-07 11:43:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863436181-4487757648: GET /api/scraping/sessions
2025-07-07 11:43:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863436181-4487757648: 200
2025-07-07 11:43:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863438235-4487755728: GET /api/scraping/sessions
2025-07-07 11:43:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863438235-4487755728: 200
2025-07-07 11:44:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863440280-4487792272: GET /api/scraping/sessions
2025-07-07 11:44:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863440280-4487792272: 200
2025-07-07 11:44:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863442330-4487019728: GET /api/scraping/sessions
2025-07-07 11:44:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863442330-4487019728: 200
2025-07-07 11:44:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863444376-4487791248: GET /api/scraping/sessions
2025-07-07 11:44:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863444376-4487791248: 200
2025-07-07 11:44:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863446416-4487019216: GET /api/scraping/sessions
2025-07-07 11:44:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863446416-4487019216: 200
2025-07-07 11:44:07 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:44:07 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:44:07 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:44:08 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:44:08 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:44:08 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:44:08 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:44:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863448469-4422074832: GET /api/scraping/sessions
2025-07-07 11:44:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863448469-4422074832: 200
2025-07-07 11:44:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863450519-4422674576: GET /api/scraping/sessions
2025-07-07 11:44:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863450519-4422674576: 200
2025-07-07 11:44:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863452568-4422744848: GET /api/scraping/sessions
2025-07-07 11:44:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863452568-4422744848: 200
2025-07-07 11:44:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863454615-4422729872: GET /api/scraping/sessions
2025-07-07 11:44:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863454615-4422729872: 200
2025-07-07 11:44:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863456667-4422675408: GET /api/scraping/sessions
2025-07-07 11:44:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863456667-4422675408: 200
2025-07-07 11:44:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863458718-4418733648: GET /api/scraping/sessions
2025-07-07 11:44:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863458718-4418733648: 200
2025-07-07 11:44:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863460768-4422744720: GET /api/scraping/sessions
2025-07-07 11:44:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863460768-4422744720: 200
2025-07-07 11:44:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:44:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:44:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:44:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:44:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:44:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:44:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:44:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863463303-4437735952: GET /api/scraping/sessions
2025-07-07 11:44:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863463303-4437735952: 200
2025-07-07 11:44:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863465342-4438386832: GET /api/scraping/sessions
2025-07-07 11:44:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863465342-4438386832: 200
2025-07-07 11:44:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863467394-4438430800: GET /api/scraping/sessions
2025-07-07 11:44:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863467394-4438430800: 200
2025-07-07 11:44:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863469445-4438385616: GET /api/scraping/sessions
2025-07-07 11:44:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863469445-4438385616: 200
2025-07-07 11:44:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751863471497-4437735696: GET /api/scraping/sessions
2025-07-07 11:44:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751863471497-4437735696: 200
2025-07-07 11:44:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863473543-4438426384: GET /api/scraping/sessions
2025-07-07 11:44:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863473543-4438426384: 200
2025-07-07 11:44:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863475595-4438491536: GET /api/scraping/sessions
2025-07-07 11:44:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863475595-4438491536: 200
2025-07-07 11:44:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863477645-4433270800: GET /api/scraping/sessions
2025-07-07 11:44:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863477645-4433270800: 200
2025-07-07 11:44:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863479697-4438492368: GET /api/scraping/sessions
2025-07-07 11:44:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863479697-4438492368: 200
2025-07-07 11:44:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863481748-4433912720: GET /api/scraping/sessions
2025-07-07 11:44:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863481748-4433912720: 200
2025-07-07 11:44:42 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:44:42 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:44:42 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:44:43 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:44:43 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:44:43 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:44:43 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:44:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863483962-4890736144: GET /api/scraping/sessions
2025-07-07 11:44:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863483962-4890736144: 200
2025-07-07 11:44:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863485999-4891408528: GET /api/scraping/sessions
2025-07-07 11:44:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863485999-4891408528: 200
2025-07-07 11:44:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863488050-4891458064: GET /api/scraping/sessions
2025-07-07 11:44:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863488050-4891458064: 200
2025-07-07 11:44:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863490100-4891457104: GET /api/scraping/sessions
2025-07-07 11:44:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863490100-4891457104: 200
2025-07-07 11:44:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863492146-4891398992: GET /api/scraping/sessions
2025-07-07 11:44:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863492146-4891398992: 200
2025-07-07 11:44:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863494200-4890827728: GET /api/scraping/sessions
2025-07-07 11:44:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863494200-4890827728: 200
2025-07-07 11:44:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863496253-4891493776: GET /api/scraping/sessions
2025-07-07 11:44:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863496253-4891493776: 200
2025-07-07 11:44:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863498308-4890738448: GET /api/scraping/sessions
2025-07-07 11:44:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863498308-4890738448: 200
2025-07-07 11:45:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863500359-4891496208: GET /api/scraping/sessions
2025-07-07 11:45:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863500359-4891496208: 200
2025-07-07 11:45:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751863502409-4891404496: GET /api/scraping/sessions
2025-07-07 11:45:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863502409-4891404496: 200
2025-07-07 11:45:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863504461-4891503504: GET /api/scraping/sessions
2025-07-07 11:45:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863504461-4891503504: 200
2025-07-07 11:45:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863506512-4890734608: GET /api/scraping/sessions
2025-07-07 11:45:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863506512-4890734608: 200
2025-07-07 11:45:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863508568-4891495504: GET /api/scraping/sessions
2025-07-07 11:45:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863508568-4891495504: 200
2025-07-07 11:45:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863510623-4883606160: GET /api/scraping/sessions
2025-07-07 11:45:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863510623-4883606160: 200
2025-07-07 11:45:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863512678-4891504144: GET /api/scraping/sessions
2025-07-07 11:45:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863512678-4891504144: 200
2025-07-07 11:45:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863514734-4884136272: GET /api/scraping/sessions
2025-07-07 11:45:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863514734-4884136272: 200
2025-07-07 11:45:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863516788-4891495632: GET /api/scraping/sessions
2025-07-07 11:45:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863516788-4891495632: 200
2025-07-07 11:45:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863518842-4891457552: GET /api/scraping/sessions
2025-07-07 11:45:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863518842-4891457552: 200
2025-07-07 11:45:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863520896-4891501392: GET /api/scraping/sessions
2025-07-07 11:45:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863520896-4891501392: 200
2025-07-07 11:45:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863522950-4890820688: GET /api/scraping/sessions
2025-07-07 11:45:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863522950-4890820688: 200
2025-07-07 11:45:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863525005-4891506832: GET /api/scraping/sessions
2025-07-07 11:45:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863525005-4891506832: 200
2025-07-07 11:45:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863527060-4891459024: GET /api/scraping/sessions
2025-07-07 11:45:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863527060-4891459024: 200
2025-07-07 11:45:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863529113-4891500496: GET /api/scraping/sessions
2025-07-07 11:45:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863529113-4891500496: 200
2025-07-07 11:45:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751863531168-4891454288: GET /api/scraping/sessions
2025-07-07 11:45:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751863531168-4891454288: 200
2025-07-07 11:45:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863533223-4891505360: GET /api/scraping/sessions
2025-07-07 11:45:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863533223-4891505360: 200
2025-07-07 11:45:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863535278-4890823248: GET /api/scraping/sessions
2025-07-07 11:45:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863535278-4890823248: 200
2025-07-07 11:45:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863537333-4891502608: GET /api/scraping/sessions
2025-07-07 11:45:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863537333-4891502608: 200
2025-07-07 11:45:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863539387-4891507920: GET /api/scraping/sessions
2025-07-07 11:45:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863539387-4891507920: 200
2025-07-07 11:45:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863541444-4891459024: GET /api/scraping/sessions
2025-07-07 11:45:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863541444-4891459024: 200
2025-07-07 11:45:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863543497-4890821584: GET /api/scraping/sessions
2025-07-07 11:45:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863543497-4890821584: 200
2025-07-07 11:45:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863545553-4891453520: GET /api/scraping/sessions
2025-07-07 11:45:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863545553-4891453520: 200
2025-07-07 11:45:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863547607-4883426960: GET /api/scraping/sessions
2025-07-07 11:45:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863547607-4883426960: 200
2025-07-07 11:45:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751863549659-4891458000: GET /api/scraping/sessions
2025-07-07 11:45:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751863549659-4891458000: 200
2025-07-07 11:45:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863551713-4891445008: GET /api/scraping/sessions
2025-07-07 11:45:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863551713-4891445008: 200
2025-07-07 11:45:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863553766-4891502672: GET /api/scraping/sessions
2025-07-07 11:45:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863553766-4891502672: 200
2025-07-07 11:45:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863555822-4891399952: GET /api/scraping/sessions
2025-07-07 11:45:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863555822-4891399952: 200
2025-07-07 11:45:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863557877-4891508624: GET /api/scraping/sessions
2025-07-07 11:45:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863557877-4891508624: 200
2025-07-07 11:45:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863559931-4891495696: GET /api/scraping/sessions
2025-07-07 11:45:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863559931-4891495696: 200
2025-07-07 11:46:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863561986-4891505808: GET /api/scraping/sessions
2025-07-07 11:46:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751863561986-4891505808: 200
2025-07-07 11:46:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751863564043-4883443600: GET /api/scraping/sessions
2025-07-07 11:46:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751863564043-4883443600: 200
2025-07-07 11:46:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751863566096-4891504464: GET /api/scraping/sessions
2025-07-07 11:46:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751863566096-4891504464: 200
2025-07-07 11:46:06 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:46:06 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:46:06 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:46:07 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:46:07 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:46:07 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:46:07 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:46:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751863568160-4436772240: GET /api/scraping/sessions
2025-07-07 11:46:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751863568160-4436772240: 200
2025-07-07 11:46:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751863570211-4437359952: GET /api/scraping/sessions
2025-07-07 11:46:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751863570211-4437359952: 200
2025-07-07 11:46:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751863572267-4437393232: GET /api/scraping/sessions
2025-07-07 11:46:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751863572267-4437393232: 200
2025-07-07 11:46:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751863574325-4437360208: GET /api/scraping/sessions
2025-07-07 11:46:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863574325-4437360208: 200
2025-07-07 11:46:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863576376-4437460816: GET /api/scraping/sessions
2025-07-07 11:46:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863576376-4437460816: 200
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577338-4437382864: GET /api/scraping/test-zendriver-service
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577338-4437382864: 200
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577347-4437466576: POST /api/scraping/sessions
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577347-4437466576: 201
2025-07-07 11:46:17 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:46:17 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:46:17 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577370-4437461200: GET /api/scraping/sessions/29
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577370-4437461200: 200
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577376-4437572624: GET /api/scraping/sessions/29
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577376-4437572624: 200
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577383-4437385872: GET /api/scraping/sessions/29/export
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577383-4437385872: 404
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577392-4446033424: GET /api/scraping/sessions/29/export
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577392-4446033424: 404
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863577400-4437572176: POST /api/scraping/sessions/29/start
2025-07-07 11:46:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863577400-4437572176: 200
2025-07-07 11:46:17 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:46:17 | ERROR | automation.browser_manager:launch_browser:79 | Profile configuration not found: default
2025-07-07 11:46:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863578438-4437572688: GET /api/scraping/sessions
2025-07-07 11:46:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863578438-4437572688: 200
2025-07-07 11:46:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863580490-4446041808: GET /api/scraping/sessions
2025-07-07 11:46:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863580490-4446041808: 200
2025-07-07 11:46:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863582546-4446030416: GET /api/scraping/sessions
2025-07-07 11:46:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863582546-4446030416: 200
2025-07-07 11:46:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863584602-4446061904: GET /api/scraping/sessions
2025-07-07 11:46:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863584602-4446061904: 200
2025-07-07 11:46:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863586674-4437473488: GET /api/scraping/sessions
2025-07-07 11:46:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863586674-4437473488: 200
2025-07-07 11:46:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863588729-4446070736: GET /api/scraping/sessions
2025-07-07 11:46:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863588729-4446070736: 200
2025-07-07 11:46:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863590784-4446073744: GET /api/scraping/sessions
2025-07-07 11:46:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863590784-4446073744: 200
2025-07-07 11:46:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863592837-4446070672: GET /api/scraping/sessions
2025-07-07 11:46:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863592837-4446070672: 200
2025-07-07 11:46:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863594893-4446030416: GET /api/scraping/sessions
2025-07-07 11:46:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863594893-4446030416: 200
2025-07-07 11:46:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863596947-4446067216: GET /api/scraping/sessions
2025-07-07 11:46:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863596947-4446067216: 200
2025-07-07 11:46:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751863599004-4414893392: GET /api/scraping/sessions
2025-07-07 11:46:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751863599004-4414893392: 200
2025-07-07 11:46:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751863601063-4437356304: GET /api/scraping/sessions
2025-07-07 11:46:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751863601063-4437356304: 200
2025-07-07 11:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751863603116-4437465424: GET /api/scraping/sessions
2025-07-07 11:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751863603116-4437465424: 200
2025-07-07 11:46:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863605166-4446074128: GET /api/scraping/sessions
2025-07-07 11:46:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863605166-4446074128: 200
2025-07-07 11:46:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863607220-4437387088: GET /api/scraping/sessions
2025-07-07 11:46:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863607220-4437387088: 200
2025-07-07 11:46:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751863609272-4446036752: GET /api/scraping/sessions
2025-07-07 11:46:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751863609272-4446036752: 200
2025-07-07 11:46:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863611326-4437472656: GET /api/scraping/sessions
2025-07-07 11:46:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863611326-4437472656: 200
2025-07-07 11:46:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863613379-4446030544: GET /api/scraping/sessions
2025-07-07 11:46:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863613379-4446030544: 200
2025-07-07 11:46:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863615436-4446039248: GET /api/scraping/sessions
2025-07-07 11:46:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863615436-4446039248: 200
2025-07-07 11:46:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863617493-4446068752: GET /api/scraping/sessions
2025-07-07 11:46:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863617493-4446068752: 200
2025-07-07 11:46:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863619549-4437570768: GET /api/scraping/sessions
2025-07-07 11:46:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863619549-4437570768: 200
2025-07-07 11:47:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863621604-4445962320: GET /api/scraping/sessions
2025-07-07 11:47:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863621604-4445962320: 200
2025-07-07 11:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863623654-4446031248: GET /api/scraping/sessions
2025-07-07 11:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863623654-4446031248: 200
2025-07-07 11:47:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863625701-4446071056: GET /api/scraping/sessions
2025-07-07 11:47:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863625701-4446071056: 200
2025-07-07 11:47:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863627753-4437573328: GET /api/scraping/sessions
2025-07-07 11:47:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863627753-4437573328: 200
2025-07-07 11:47:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863629816-4446042960: GET /api/scraping/sessions
2025-07-07 11:47:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863629816-4446042960: 200
2025-07-07 11:47:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863631862-4446039760: GET /api/scraping/sessions
2025-07-07 11:47:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863631862-4446039760: 200
2025-07-07 11:47:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863633915-4437461392: GET /api/scraping/sessions
2025-07-07 11:47:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863633915-4437461392: 200
2025-07-07 11:47:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863635968-4414891280: GET /api/scraping/sessions
2025-07-07 11:47:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863635968-4414891280: 200
2025-07-07 11:47:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863638022-4446042640: GET /api/scraping/sessions
2025-07-07 11:47:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863638022-4446042640: 200
2025-07-07 11:47:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863640078-4446039248: GET /api/scraping/sessions
2025-07-07 11:47:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863640078-4446039248: 200
2025-07-07 11:47:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863642135-4446067920: GET /api/scraping/sessions
2025-07-07 11:47:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863642135-4446067920: 200
2025-07-07 11:47:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863644190-4414887632: GET /api/scraping/sessions
2025-07-07 11:47:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863644190-4414887632: 200
2025-07-07 11:47:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863646244-4445964240: GET /api/scraping/sessions
2025-07-07 11:47:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863646244-4445964240: 200
2025-07-07 11:47:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863648296-4437356496: GET /api/scraping/sessions
2025-07-07 11:47:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863648296-4437356496: 200
2025-07-07 11:47:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863650364-4437472528: GET /api/scraping/sessions
2025-07-07 11:47:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863650364-4437472528: 200
2025-07-07 11:47:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863652438-4446040528: GET /api/scraping/sessions
2025-07-07 11:47:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863652438-4446040528: 200
2025-07-07 11:47:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863654495-4437570768: GET /api/scraping/sessions
2025-07-07 11:47:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863654495-4437570768: 200
2025-07-07 11:47:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863656545-4437389136: GET /api/scraping/sessions
2025-07-07 11:47:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863656545-4437389136: 200
2025-07-07 11:47:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863657873-4437466192: GET /api/scraping/test-zendriver-service
2025-07-07 11:47:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863657873-4437466192: 200
2025-07-07 11:47:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751863657884-4446030480: POST /api/scraping/sessions
2025-07-07 11:47:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751863657884-4446030480: 201
2025-07-07 11:47:37 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:47:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863658599-4445968464: GET /api/scraping/sessions
2025-07-07 11:47:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863658599-4445968464: 200
2025-07-07 11:47:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863660781-4446196432: GET /api/scraping/sessions
2025-07-07 11:47:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863660781-4446196432: 200
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863662824-4446195856: GET /api/scraping/sessions
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863662824-4446195856: 200
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863662910-4446224976: GET /api/scraping/sessions/30
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863662910-4446224976: 200
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863662922-4445965776: GET /api/scraping/sessions/30/export
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863662922-4445965776: 404
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863662932-4446234896: GET /api/scraping/sessions/30/export
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863662932-4446234896: 404
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863662943-4446193616: POST /api/scraping/sessions/30/start
2025-07-07 11:47:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863662943-4446193616: 400
2025-07-07 11:47:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863664881-4446273808: GET /api/scraping/sessions
2025-07-07 11:47:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863664881-4446273808: 200
2025-07-07 11:47:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863666935-4446105360: GET /api/scraping/sessions
2025-07-07 11:47:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863666935-4446105360: 200
2025-07-07 11:47:47 | ERROR | automation.browser_manager:launch_browser:132 | Failed to launch browser for profile default: 'Browser' object has no attribute 'execute_cdp_cmd'
2025-07-07 11:47:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751863667940-4445963280: GET /api/scraping/sessions/30
2025-07-07 11:47:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751863667940-4445963280: 200
2025-07-07 11:47:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863668990-4446230416: GET /api/scraping/sessions
2025-07-07 11:47:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863668990-4446230416: 200
2025-07-07 11:47:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863671036-4446240464: GET /api/scraping/sessions
2025-07-07 11:47:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863671036-4446240464: 200
2025-07-07 11:47:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863671134-4446095760: GET /api/scraping/sessions/30
2025-07-07 11:47:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863671134-4446095760: 200
2025-07-07 11:47:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863673080-4446076752: GET /api/scraping/sessions
2025-07-07 11:47:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863673080-4446076752: 200
2025-07-07 11:47:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863675135-4446030672: GET /api/scraping/sessions
2025-07-07 11:47:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863675135-4446030672: 200
2025-07-07 11:47:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863677183-4414697424: GET /api/scraping/sessions
2025-07-07 11:47:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863677183-4414697424: 200
2025-07-07 11:47:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863679238-4437356240: GET /api/scraping/sessions
2025-07-07 11:47:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863679238-4437356240: 200
2025-07-07 11:48:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863681298-4446106832: GET /api/scraping/sessions
2025-07-07 11:48:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863681298-4446106832: 200
2025-07-07 11:48:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863683353-4446100304: GET /api/scraping/sessions
2025-07-07 11:48:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863683353-4446100304: 200
2025-07-07 11:48:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863685409-4446061392: GET /api/scraping/sessions
2025-07-07 11:48:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863685409-4446061392: 200
2025-07-07 11:48:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863687455-4437352208: GET /api/scraping/sessions
2025-07-07 11:48:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863687455-4437352208: 200
2025-07-07 11:48:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863689511-4437383248: GET /api/scraping/sessions
2025-07-07 11:48:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863689511-4437383248: 200
2025-07-07 11:48:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863691638-4446102352: GET /api/scraping/sessions
2025-07-07 11:48:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863691638-4446102352: 200
2025-07-07 11:48:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863693695-4446100688: GET /api/scraping/sessions
2025-07-07 11:48:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863693695-4446100688: 200
2025-07-07 11:48:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863695751-4437470608: GET /api/scraping/sessions
2025-07-07 11:48:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863695751-4437470608: 200
2025-07-07 11:48:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863697808-4413038544: GET /api/scraping/sessions
2025-07-07 11:48:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863697808-4413038544: 200
2025-07-07 11:48:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751863699862-4446099024: GET /api/scraping/sessions
2025-07-07 11:48:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751863699862-4446099024: 200
2025-07-07 11:48:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751863701916-4446232528: GET /api/scraping/sessions
2025-07-07 11:48:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751863701916-4446232528: 200
2025-07-07 11:48:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863703970-4446099728: GET /api/scraping/sessions
2025-07-07 11:48:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863703970-4446099728: 200
2025-07-07 11:48:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863706029-4446098000: GET /api/scraping/sessions
2025-07-07 11:48:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863706029-4446098000: 200
2025-07-07 11:48:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863708085-4446101776: GET /api/scraping/sessions
2025-07-07 11:48:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863708085-4446101776: 200
2025-07-07 11:48:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863710141-4446235216: GET /api/scraping/sessions
2025-07-07 11:48:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863710141-4446235216: 200
2025-07-07 11:48:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863712196-4446101392: GET /api/scraping/sessions
2025-07-07 11:48:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863712196-4446101392: 200
2025-07-07 11:48:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863714250-4446093968: GET /api/scraping/sessions
2025-07-07 11:48:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863714250-4446093968: 200
2025-07-07 11:48:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863716310-4446108752: GET /api/scraping/sessions
2025-07-07 11:48:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863716310-4446108752: 200
2025-07-07 11:48:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863718365-4445967120: GET /api/scraping/sessions
2025-07-07 11:48:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863718365-4445967120: 200
2025-07-07 11:48:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863720420-4446237136: GET /api/scraping/sessions
2025-07-07 11:48:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863720420-4446237136: 200
2025-07-07 11:48:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863722471-4446069968: GET /api/scraping/sessions
2025-07-07 11:48:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863722471-4446069968: 200
2025-07-07 11:48:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863724526-4446098768: GET /api/scraping/sessions
2025-07-07 11:48:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863724526-4446098768: 200
2025-07-07 11:48:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863726580-4446240080: GET /api/scraping/sessions
2025-07-07 11:48:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863726580-4446240080: 200
2025-07-07 11:48:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863728635-4437347152: GET /api/scraping/sessions
2025-07-07 11:48:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863728635-4437347152: 200
2025-07-07 11:48:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863730689-4446096912: GET /api/scraping/sessions
2025-07-07 11:48:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863730689-4446096912: 200
2025-07-07 11:48:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751863732737-4446076752: GET /api/scraping/sessions
2025-07-07 11:48:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751863732737-4446076752: 200
2025-07-07 11:48:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751863734792-4446035088: GET /api/scraping/sessions
2025-07-07 11:48:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751863734792-4446035088: 200
2025-07-07 11:48:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863736851-4446096464: GET /api/scraping/sessions
2025-07-07 11:48:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863736851-4446096464: 200
2025-07-07 11:48:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863738904-4446228624: GET /api/scraping/sessions
2025-07-07 11:48:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863738904-4446228624: 200
2025-07-07 11:49:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751863740957-4446030224: GET /api/scraping/sessions
2025-07-07 11:49:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751863740957-4446030224: 200
2025-07-07 11:49:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863743014-4446040912: GET /api/scraping/sessions
2025-07-07 11:49:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863743014-4446040912: 200
2025-07-07 11:49:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863745069-4445966928: GET /api/scraping/sessions
2025-07-07 11:49:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863745069-4445966928: 200
2025-07-07 11:49:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863747127-4414689680: GET /api/scraping/sessions
2025-07-07 11:49:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863747127-4414689680: 200
2025-07-07 11:49:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863749178-4446042832: GET /api/scraping/sessions
2025-07-07 11:49:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863749178-4446042832: 200
2025-07-07 11:49:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863751228-4446030480: GET /api/scraping/sessions
2025-07-07 11:49:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863751228-4446030480: 200
2025-07-07 11:49:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863753283-4446227216: GET /api/scraping/sessions
2025-07-07 11:49:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863753283-4446227216: 200
2025-07-07 11:49:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863755339-4437571216: GET /api/scraping/sessions
2025-07-07 11:49:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863755339-4437571216: 200
2025-07-07 11:49:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863757395-4446073040: GET /api/scraping/sessions
2025-07-07 11:49:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863757395-4446073040: 200
2025-07-07 11:49:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751863759457-4414704720: GET /api/scraping/sessions
2025-07-07 11:49:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751863759457-4414704720: 200
2025-07-07 11:49:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751863761524-4445965328: GET /api/scraping/sessions
2025-07-07 11:49:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751863761524-4445965328: 200
2025-07-07 11:49:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751863763589-4446075024: GET /api/scraping/sessions
2025-07-07 11:49:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751863763589-4446075024: 200
2025-07-07 11:49:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751863765642-4446042832: GET /api/scraping/sessions
2025-07-07 11:49:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751863765642-4446042832: 200
2025-07-07 11:49:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751863767702-4414046736: GET /api/scraping/sessions
2025-07-07 11:49:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751863767702-4414046736: 200
2025-07-07 11:49:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751863769761-4445964944: GET /api/scraping/sessions
2025-07-07 11:49:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751863769761-4445964944: 200
2025-07-07 11:49:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751863771815-4415023440: GET /api/scraping/sessions
2025-07-07 11:49:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751863771815-4415023440: 200
2025-07-07 11:49:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751863773867-4446034320: GET /api/scraping/sessions
2025-07-07 11:49:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751863773867-4446034320: 200
2025-07-07 11:49:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751863775921-4445963280: GET /api/scraping/sessions
2025-07-07 11:49:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751863775921-4445963280: 200
2025-07-07 11:49:36 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:49:36 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:49:36 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:49:40 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:49:40 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:49:40 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:49:40 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:49:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863780763-4405230032: GET /api/scraping/sessions
2025-07-07 11:49:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863780763-4405230032: 200
2025-07-07 11:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863782812-4405897424: GET /api/scraping/sessions
2025-07-07 11:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863782812-4405897424: 200
2025-07-07 11:49:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863784868-4405935376: GET /api/scraping/sessions
2025-07-07 11:49:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863784868-4405935376: 200
2025-07-07 11:49:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863785402-4405894736: GET /api/scraping/test-zendriver-service
2025-07-07 11:49:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863785402-4405894736: 200
2025-07-07 11:49:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751863785411-4406002960: POST /api/scraping/sessions
2025-07-07 11:49:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751863785411-4406002960: 201
2025-07-07 11:49:45 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:49:45 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:49:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863786934-4406015504: GET /api/scraping/sessions
2025-07-07 11:49:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863786934-4406015504: 200
2025-07-07 11:49:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863788985-4406132560: GET /api/scraping/sessions
2025-07-07 11:49:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863788985-4406132560: 200
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863790440-4406150864: GET /api/scraping/sessions/31
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863790440-4406150864: 200
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863790463-4406123664: GET /api/scraping/sessions/31/export
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863790463-4406123664: 404
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863790478-4406273360: GET /api/scraping/sessions/31/export
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863790478-4406273360: 404
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751863790487-4406270224: POST /api/scraping/sessions/31/start
2025-07-07 11:49:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751863790487-4406270224: 400
2025-07-07 11:49:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863791039-4406327056: GET /api/scraping/sessions
2025-07-07 11:49:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863791039-4406327056: 200
2025-07-07 11:49:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863793091-4406270096: GET /api/scraping/sessions
2025-07-07 11:49:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863793091-4406270096: 200
2025-07-07 11:49:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863795149-4406320912: GET /api/scraping/sessions
2025-07-07 11:49:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863795149-4406320912: 200
2025-07-07 11:49:55 | WARNING | automation.browser_manager:launch_browser:115 | Could not set user agent: 'Browser' object has no attribute 'get_page'
2025-07-07 11:49:55 | WARNING | automation.browser_manager:launch_browser:124 | Could not set timezone: 'Browser' object has no attribute 'get_page'
2025-07-07 11:49:55 | WARNING | automation.browser_manager:launch_browser:133 | Could not set language: 'Browser' object has no attribute 'get_page'
2025-07-07 11:49:55 | INFO | automation.browser_manager:launch_browser:136 | Browser launched successfully for profile: default
2025-07-07 11:49:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751863796781-4406314064: GET /api/scraping/sessions/31
2025-07-07 11:49:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751863796781-4406314064: 200
2025-07-07 11:49:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863797205-4406266960: GET /api/scraping/sessions
2025-07-07 11:49:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863797205-4406266960: 200
2025-07-07 11:49:58 | INFO | automation.facebook_session:initiate_login:61 | Facebook login initiated for profile: default
2025-07-07 11:49:58 | INFO | automation.facebook_scraper_service:_ensure_facebook_session:531 | Waiting for manual login completion...
2025-07-07 11:49:58 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:49:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863799262-4406336336: GET /api/scraping/sessions
2025-07-07 11:49:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863799262-4406336336: 200
2025-07-07 11:50:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863801325-4406316432: GET /api/scraping/sessions
2025-07-07 11:50:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863801325-4406316432: 200
2025-07-07 11:50:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863803379-4406341328: GET /api/scraping/sessions
2025-07-07 11:50:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863803379-4406341328: 200
2025-07-07 11:50:03 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863805437-4406329552: GET /api/scraping/sessions
2025-07-07 11:50:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863805437-4406329552: 200
2025-07-07 11:50:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863807495-4406340688: GET /api/scraping/sessions
2025-07-07 11:50:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863807495-4406340688: 200
2025-07-07 11:50:08 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863809539-4406124304: GET /api/scraping/sessions
2025-07-07 11:50:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863809539-4406124304: 200
2025-07-07 11:50:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863811601-4406339280: GET /api/scraping/sessions
2025-07-07 11:50:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863811601-4406339280: 200
2025-07-07 11:50:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863813653-4406342544: GET /api/scraping/sessions
2025-07-07 11:50:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863813653-4406342544: 200
2025-07-07 11:50:13 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863815712-4406017168: GET /api/scraping/sessions
2025-07-07 11:50:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863815712-4406017168: 200
2025-07-07 11:50:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863817771-4406272592: GET /api/scraping/sessions
2025-07-07 11:50:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863817771-4406272592: 200
2025-07-07 11:50:18 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:23 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:28 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:33 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:38 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:43 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:48 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:53 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:50:58 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:03 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:08 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:13 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:18 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:23 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:28 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:33 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:38 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:43 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:48 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:53 | ERROR | automation.facebook_session:check_login_status:122 | Failed to check login status for profile default: 'Browser' object has no attribute 'get_current_page'
2025-07-07 11:51:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751863918946-4405930768: GET /api/scraping/sessions/31
2025-07-07 11:51:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751863918946-4405930768: 200
2025-07-07 11:51:59 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:51:59 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:51:59 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:52:03 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:52:03 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:52:03 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:52:03 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923557-**********: GET /api/scraping/sessions
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923559-**********: GET /health
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923560-**********: GET /api/scraping/sessions
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923562-**********: GET /api/scraping/sessions
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923559-**********: 200
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923562-**********: 200
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923557-**********: 200
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923560-**********: 200
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923679-**********: GET /api/profiles
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923679-**********: 307
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863923712-4654636432: GET /api/profiles/
2025-07-07 11:52:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863923712-4654636432: 200
2025-07-07 11:52:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863925607-4654629968: GET /api/scraping/sessions
2025-07-07 11:52:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863925607-4654629968: 200
2025-07-07 11:52:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863927668-4652991056: GET /api/scraping/sessions
2025-07-07 11:52:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863927668-4652991056: 200
2025-07-07 11:52:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863929723-4652829072: GET /api/scraping/sessions
2025-07-07 11:52:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863929723-4652829072: 200
2025-07-07 11:52:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863931778-4652764304: GET /api/scraping/sessions
2025-07-07 11:52:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863931778-4652764304: 200
2025-07-07 11:52:12 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:52:12 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:52:12 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:52:13 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:52:13 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:52:13 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:52:13 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:52:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863933999-4584621712: GET /api/scraping/sessions
2025-07-07 11:52:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751863933999-4584621712: 200
2025-07-07 11:52:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751863936035-4585205328: GET /api/scraping/sessions
2025-07-07 11:52:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751863936035-4585205328: 200
2025-07-07 11:52:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751863938093-4585258896: GET /api/scraping/sessions
2025-07-07 11:52:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751863938093-4585258896: 200
2025-07-07 11:52:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751863940165-4585196432: GET /api/scraping/sessions
2025-07-07 11:52:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751863940165-4585196432: 200
2025-07-07 11:52:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751863942222-4585309968: GET /api/scraping/sessions
2025-07-07 11:52:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751863942222-4585309968: 200
2025-07-07 11:52:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751863944275-4585258320: GET /api/scraping/sessions
2025-07-07 11:52:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751863944275-4585258320: 200
2025-07-07 11:52:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751863946349-4585311184: GET /api/scraping/sessions
2025-07-07 11:52:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751863946349-4585311184: 200
2025-07-07 11:52:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751863948414-4585202512: GET /api/scraping/sessions
2025-07-07 11:52:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751863948414-4585202512: 200
2025-07-07 11:52:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751863950480-4585318096: GET /api/scraping/sessions
2025-07-07 11:52:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751863950480-4585318096: 200
2025-07-07 11:52:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751863952543-4584622544: GET /api/scraping/sessions
2025-07-07 11:52:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751863952543-4584622544: 200
2025-07-07 11:52:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751863954605-4585321808: GET /api/scraping/sessions
2025-07-07 11:52:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751863954605-4585321808: 200
2025-07-07 11:52:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751863956662-4585258320: GET /api/scraping/sessions
2025-07-07 11:52:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751863956662-4585258320: 200
2025-07-07 11:52:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751863958719-4585312144: GET /api/scraping/sessions
2025-07-07 11:52:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751863958719-4585312144: 200
2025-07-07 11:52:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751863960780-4584631184: GET /api/scraping/sessions
2025-07-07 11:52:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751863960780-4584631184: 200
2025-07-07 11:52:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751863962836-4585315664: GET /api/scraping/sessions
2025-07-07 11:52:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751863962836-4585315664: 200
2025-07-07 11:52:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751863964874-4585258256: GET /api/scraping/sessions
2025-07-07 11:52:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751863964874-4585258256: 200
2025-07-07 11:52:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751863966933-4585317328: GET /api/scraping/sessions
2025-07-07 11:52:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751863966933-4585317328: 200
2025-07-07 11:52:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751863968991-4584620112: GET /api/scraping/sessions
2025-07-07 11:52:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751863968991-4584620112: 200
2025-07-07 11:52:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751863971044-4585313808: GET /api/scraping/sessions
2025-07-07 11:52:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751863971044-4585313808: 200
2025-07-07 11:52:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751863973094-4585244048: GET /api/scraping/sessions
2025-07-07 11:52:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751863973094-4585244048: 200
2025-07-07 11:52:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751863975153-4585322768: GET /api/scraping/sessions
2025-07-07 11:52:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751863975153-4585322768: 200
2025-07-07 11:52:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751863977213-4584629520: GET /api/scraping/sessions
2025-07-07 11:52:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751863977213-4584629520: 200
2025-07-07 11:52:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751863979271-4585310032: GET /api/scraping/sessions
2025-07-07 11:52:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751863979271-4585310032: 200
2025-07-07 11:53:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751863981332-4585253776: GET /api/scraping/sessions
2025-07-07 11:53:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751863981332-4585253776: 200
2025-07-07 11:53:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751863983394-4585308432: GET /api/scraping/sessions
2025-07-07 11:53:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751863983394-4585308432: 200
2025-07-07 11:53:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751863985452-4584629392: GET /api/scraping/sessions
2025-07-07 11:53:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751863985452-4584629392: 200
2025-07-07 11:53:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751863987511-4585311056: GET /api/scraping/sessions
2025-07-07 11:53:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751863987511-4585311056: 200
2025-07-07 11:53:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751863989566-4585314128: GET /api/scraping/sessions
2025-07-07 11:53:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751863989566-4585314128: 200
2025-07-07 11:53:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751863991629-4585247568: GET /api/scraping/sessions
2025-07-07 11:53:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751863991629-4585247568: 200
2025-07-07 11:53:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751863993689-4584626064: GET /api/scraping/sessions
2025-07-07 11:53:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751863993689-4584626064: 200
2025-07-07 11:53:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751863995748-4585310992: GET /api/scraping/sessions
2025-07-07 11:53:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751863995748-4585310992: 200
2025-07-07 11:53:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751863997808-4585252432: GET /api/scraping/sessions
2025-07-07 11:53:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751863997808-4585252432: 200
2025-07-07 11:53:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751863999866-4585317840: GET /api/scraping/sessions
2025-07-07 11:53:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751863999866-4585317840: 200
2025-07-07 11:53:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751864001925-4585312336: GET /api/scraping/sessions
2025-07-07 11:53:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751864001925-4585312336: 200
2025-07-07 11:53:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:53:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:53:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:53:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:53:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751864003982-4585253584: GET /api/scraping/sessions
2025-07-07 11:53:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751864003982-4585253584: 200
2025-07-07 11:53:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751864006034-4585317520: GET /api/scraping/sessions
2025-07-07 11:53:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751864006034-4585317520: 200
2025-07-07 11:53:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751864008094-4585251280: GET /api/scraping/sessions
2025-07-07 11:53:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751864008094-4585251280: 200
2025-07-07 11:53:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751864010151-4585258896: GET /api/scraping/sessions
2025-07-07 11:53:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751864010151-4585258896: 200
2025-07-07 11:53:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751864012214-4585207504: GET /api/scraping/sessions
2025-07-07 11:53:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751864012214-4585207504: 200
2025-07-07 11:53:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751864014276-4585243792: GET /api/scraping/sessions
2025-07-07 11:53:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751864014276-4585243792: 200
2025-07-07 11:53:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751864016337-4585253712: GET /api/scraping/sessions
2025-07-07 11:53:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751864016337-4585253712: 200
2025-07-07 11:53:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751864018398-4585254736: GET /api/scraping/sessions
2025-07-07 11:53:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751864018398-4585254736: 200
2025-07-07 11:53:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751864020458-4584620816: GET /api/scraping/sessions
2025-07-07 11:53:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751864020458-4584620816: 200
2025-07-07 11:53:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751864022516-4585323856: GET /api/scraping/sessions
2025-07-07 11:53:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751864022516-4585323856: 200
2025-07-07 11:53:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751864024573-4585308688: GET /api/scraping/sessions
2025-07-07 11:53:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751864024573-4585308688: 200
2025-07-07 11:53:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751864026628-4584623568: GET /api/scraping/sessions
2025-07-07 11:53:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751864026628-4584623568: 200
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751864028336-4584630096: GET /api/scraping/test-zendriver-service
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751864028336-4584630096: 200
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751864028345-4585258768: POST /api/scraping/sessions
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751864028345-4585258768: 201
2025-07-07 11:53:48 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:53:48 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/591054007361950/posts/1234567890
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751864028685-4585405712: GET /api/scraping/sessions
2025-07-07 11:53:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751864028685-4585405712: 200
2025-07-07 11:53:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751864030736-4586489488: GET /api/scraping/sessions
2025-07-07 11:53:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751864030736-4586489488: 200
2025-07-07 11:53:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751864032799-4586497808: GET /api/scraping/sessions
2025-07-07 11:53:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751864032799-4586497808: 200
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751864033376-4586543312: GET /api/scraping/sessions/32
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751864033376-4586543312: 200
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751864033398-4586538832: GET /api/scraping/sessions/32/export
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751864033398-4586538832: 404
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751864033412-4586689104: GET /api/scraping/sessions/32/export
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751864033412-4586689104: 404
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751864033421-4586690256: POST /api/scraping/sessions/32/start
2025-07-07 11:53:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751864033421-4586690256: 400
2025-07-07 11:53:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751864034857-4586724816: GET /api/scraping/sessions
2025-07-07 11:53:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751864034857-4586724816: 200
2025-07-07 11:53:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751864036916-4586690576: GET /api/scraping/sessions
2025-07-07 11:53:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751864036916-4586690576: 200
2025-07-07 11:53:58 | WARNING | automation.browser_manager:launch_browser:115 | Could not set user agent: 'Browser' object has no attribute 'get_page'
2025-07-07 11:53:58 | WARNING | automation.browser_manager:launch_browser:124 | Could not set timezone: 'Browser' object has no attribute 'get_page'
2025-07-07 11:53:58 | WARNING | automation.browser_manager:launch_browser:133 | Could not set language: 'Browser' object has no attribute 'get_page'
2025-07-07 11:53:58 | INFO | automation.browser_manager:launch_browser:136 | Browser launched successfully for profile: default
2025-07-07 11:53:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751864038982-4586694608: GET /api/scraping/sessions
2025-07-07 11:53:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751864038982-4586694608: 200
2025-07-07 11:54:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751864041028-4586688528: GET /api/scraping/sessions
2025-07-07 11:54:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751864041028-4586688528: 200
2025-07-07 11:54:01 | INFO | automation.facebook_session:initiate_login:61 | Facebook login initiated for profile: default
2025-07-07 11:54:01 | INFO | automation.facebook_scraper_service:_ensure_facebook_session:531 | Waiting for manual login completion...
2025-07-07 11:54:01 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751864043089-4586717648: GET /api/scraping/sessions
2025-07-07 11:54:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751864043089-4586717648: 200
2025-07-07 11:54:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751864045147-4586723920: GET /api/scraping/sessions
2025-07-07 11:54:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751864045147-4586723920: 200
2025-07-07 11:54:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751864046374-4586491856: GET /api/scraping/sessions/32
2025-07-07 11:54:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751864046374-4586491856: 200
2025-07-07 11:54:06 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751864047209-4586688016: GET /api/scraping/sessions
2025-07-07 11:54:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751864047209-4586688016: 200
2025-07-07 11:54:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751864049268-4586685392: GET /api/scraping/sessions
2025-07-07 11:54:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751864049268-4586685392: 200
2025-07-07 11:54:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751864051328-4586688848: GET /api/scraping/sessions
2025-07-07 11:54:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751864051328-4586688848: 200
2025-07-07 11:54:11 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751864053386-4586548688: GET /api/scraping/sessions
2025-07-07 11:54:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751864053386-4586548688: 200
2025-07-07 11:54:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751864055435-4586501584: GET /api/scraping/sessions
2025-07-07 11:54:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751864055435-4586501584: 200
2025-07-07 11:54:16 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751864057492-4585202640: GET /api/scraping/sessions
2025-07-07 11:54:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751864057492-4585202640: 200
2025-07-07 11:54:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751864059551-4586764112: GET /api/scraping/sessions
2025-07-07 11:54:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751864059551-4586764112: 200
2025-07-07 11:54:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751864061609-4586717456: GET /api/scraping/sessions
2025-07-07 11:54:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751864061609-4586717456: 200
2025-07-07 11:54:21 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751864063670-4586686288: GET /api/scraping/sessions
2025-07-07 11:54:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751864063670-4586686288: 200
2025-07-07 11:54:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751864065735-4586700560: GET /api/scraping/sessions
2025-07-07 11:54:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751864065735-4586700560: 200
2025-07-07 11:54:26 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751864067792-4586543120: GET /api/scraping/sessions
2025-07-07 11:54:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751864067792-4586543120: 200
2025-07-07 11:54:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751864069853-4586501648: GET /api/scraping/sessions
2025-07-07 11:54:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751864069853-4586501648: 200
2025-07-07 11:54:31 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751864071908-4586763792: GET /api/scraping/sessions
2025-07-07 11:54:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751864071908-4586763792: 200
2025-07-07 11:54:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751864073966-4586500560: GET /api/scraping/sessions
2025-07-07 11:54:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751864073966-4586500560: 200
2025-07-07 11:54:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751864076015-4586728272: GET /api/scraping/sessions
2025-07-07 11:54:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751864076015-4586728272: 200
2025-07-07 11:54:36 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:54:36 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:54:36 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:54:36 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:37 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:54:37 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:54:37 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:54:37 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:54:41 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:46 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:51 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:56 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:54:58 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:54:58 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:54:58 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:54:59 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:54:59 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:54:59 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:54:59 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:55:01 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:06 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:11 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:16 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:21 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:21 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:55:21 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:55:21 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:55:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:55:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:55:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:55:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:55:26 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:31 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:36 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:41 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:46 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:51 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:55:56 | ERROR | automation.facebook_session:check_login_status:90 | Failed to get page: 'Browser' object has no attribute 'get_page'
2025-07-07 11:56:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751864161869-4585258512: GET /api/scraping/sessions/32
2025-07-07 11:56:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751864161869-4585258512: 200
2025-07-07 11:56:01 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:56:01 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:56:01 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:56:06 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:56:06 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:56:06 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:56:06 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751864166381-4458813264: GET /api/scraping/sessions
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751864166383-4458892112: GET /api/scraping/sessions
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751864166384-4458903248: GET /api/scraping/sessions
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751864166381-4458813264: 200
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751864166383-4458892112: 200
2025-07-07 11:56:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751864166384-4458903248: 200

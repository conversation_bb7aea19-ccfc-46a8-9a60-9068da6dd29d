2025-07-07 10:20:11 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751858411444-4446660688: GET /api/scraping/sessions
2025-07-07 10:20:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751858411444-4446660688: 200
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751858413491-4446700240: GET /api/scraping/sessions
2025-07-07 10:20:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751858413491-4446700240: 200
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858415539-4446704272: GET /api/scraping/sessions
2025-07-07 10:20:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858415539-4446704272: 200
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858417587-4446709456: GET /api/scraping/sessions
2025-07-07 10:20:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858417587-4446709456: 200
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858419631-4446714576: GET /api/scraping/sessions
2025-07-07 10:20:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858419631-4446714576: 200
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858421679-4446659856: GET /api/scraping/sessions
2025-07-07 10:20:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858421679-4446659856: 200
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858423727-4446662352: GET /api/scraping/sessions
2025-07-07 10:20:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858423727-4446662352: 200
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858425773-4446706768: GET /api/scraping/sessions
2025-07-07 10:20:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858425773-4446706768: 200
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858427817-4446665168: GET /api/scraping/sessions
2025-07-07 10:20:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858427817-4446665168: 200
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858429862-4446706576: GET /api/scraping/sessions
2025-07-07 10:20:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858429862-4446706576: 200
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858431897-4446705232: GET /api/scraping/sessions
2025-07-07 10:20:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858431897-4446705232: 200
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751858433936-4446664592: GET /api/scraping/sessions
2025-07-07 10:20:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751858433936-4446664592: 200
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751858435982-4446445840: GET /api/scraping/sessions
2025-07-07 10:20:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751858435982-4446445840: 200
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858438025-4446713296: GET /api/scraping/sessions
2025-07-07 10:20:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858438025-4446713296: 200
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858440072-4446701392: GET /api/scraping/sessions
2025-07-07 10:20:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858440072-4446701392: 200
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858442112-4446658448: GET /api/scraping/sessions
2025-07-07 10:20:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858442112-4446658448: 200
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751858444157-4445832144: GET /api/scraping/sessions
2025-07-07 10:20:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751858444157-4445832144: 200
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751858446194-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751858446194-4446713552: 200
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751858448231-4446666512: GET /api/scraping/sessions
2025-07-07 10:20:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751858448231-4446666512: 200
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858450271-4446713552: GET /api/scraping/sessions
2025-07-07 10:20:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858450271-4446713552: 200
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751858452315-4446704656: GET /api/scraping/sessions
2025-07-07 10:20:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751858452315-4446704656: 200
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751858454364-4446700368: GET /api/scraping/sessions
2025-07-07 10:20:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751858454364-4446700368: 200
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751858456410-4446658768: GET /api/scraping/sessions
2025-07-07 10:20:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751858456410-4446658768: 200
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751858458455-4446705552: GET /api/scraping/sessions
2025-07-07 10:20:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751858458455-4446705552: 200
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751858460498-4446715024: GET /api/scraping/sessions
2025-07-07 10:21:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751858460498-4446715024: 200
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858462540-4446664592: GET /api/scraping/sessions
2025-07-07 10:21:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858462540-4446664592: 200
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751858464588-4446630032: GET /api/scraping/sessions
2025-07-07 10:21:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751858464588-4446630032: 200
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751858466634-4446699920: GET /api/scraping/sessions
2025-07-07 10:21:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751858466634-4446699920: 200
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751858468680-4446701776: GET /api/scraping/sessions
2025-07-07 10:21:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751858468680-4446701776: 200
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751858470728-4446663184: GET /api/scraping/sessions
2025-07-07 10:21:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751858470728-4446663184: 200
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751858472777-4446665680: GET /api/scraping/sessions
2025-07-07 10:21:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751858472777-4446665680: 200
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751858474825-4446703504: GET /api/scraping/sessions
2025-07-07 10:21:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751858474825-4446703504: 200
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751858476875-4446708752: GET /api/scraping/sessions
2025-07-07 10:21:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751858476875-4446708752: 200
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751858478922-4446705424: GET /api/scraping/sessions
2025-07-07 10:21:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751858478922-4446705424: 200
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751858480961-4446712720: GET /api/scraping/sessions
2025-07-07 10:21:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751858480961-4446712720: 200
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858483006-4446651408: GET /api/scraping/sessions
2025-07-07 10:21:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858483006-4446651408: 200
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858485053-4446554256: GET /api/scraping/sessions
2025-07-07 10:21:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858485053-4446554256: 200
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858487100-4446703376: GET /api/scraping/sessions
2025-07-07 10:21:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858487100-4446703376: 200
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858489143-4446709840: GET /api/scraping/sessions
2025-07-07 10:21:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858489143-4446709840: 200
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858491184-4446662416: GET /api/scraping/sessions
2025-07-07 10:21:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858491184-4446662416: 200
2025-07-07 10:22:14 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:22:14 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:22:14 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:22:14 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535407-4461760784: GET /api/scraping/sessions
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535407-4461760784: 200
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535546-4462398800: GET /api/profiles
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535546-4462398800: 307
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751858535557-4462399248: GET /api/profiles/
2025-07-07 10:22:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751858535557-4462399248: 200
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858537455-4462469008: GET /api/scraping/sessions
2025-07-07 10:22:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858537455-4462469008: 200
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751858539494-4462475152: GET /api/scraping/sessions
2025-07-07 10:22:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751858539494-4462475152: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541268-4462406992: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541269-4462405968: GET /api/scraping/sessions
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541271-4462608912: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541277-4461759248: GET /api/profiles
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541271-4462608912: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541277-4461759248: 307
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541288-4462660048: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751858541290-4462668560: GET /api/profiles/
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541269-4462405968: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541268-4462406992: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541290-4462668560: 200
2025-07-07 10:22:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751858541288-4462660048: 200
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751858543370-4462668944: GET /api/scraping/sessions
2025-07-07 10:22:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751858543370-4462668944: 200
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751858545419-4473227856: GET /api/scraping/sessions
2025-07-07 10:22:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751858545419-4473227856: 200
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751858547467-4462620048: GET /api/scraping/sessions
2025-07-07 10:22:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751858547467-4462620048: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549655-4462397264: OPTIONS /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549655-4462397264: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549661-4462471504: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549670-4462609488: DELETE /api/scraping/sessions/20
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549661-4462471504: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549670-4462609488: 200
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751858549714-4462462480: GET /api/scraping/sessions
2025-07-07 10:22:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751858549714-4462462480: 200
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751858551746-4473300496: GET /api/scraping/sessions
2025-07-07 10:22:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751858551746-4473300496: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554233-4462472464: OPTIONS /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554233-4462472464: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554237-4462623760: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554247-4473301264: DELETE /api/scraping/sessions/16
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554237-4462623760: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554247-4473301264: 200
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751858554290-4473279504: GET /api/scraping/sessions
2025-07-07 10:22:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751858554290-4473279504: 200
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751858556317-4473284688: GET /api/scraping/sessions
2025-07-07 10:22:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751858556317-4473284688: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558623-4473279184: OPTIONS /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558623-4473279184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558628-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558638-4473277136: DELETE /api/scraping/sessions/23
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558628-4462671184: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558638-4473277136: 200
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751858558696-4462671184: GET /api/scraping/sessions
2025-07-07 10:22:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751858558696-4462671184: 200
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560636-4473243792: POST /api/scraping/sessions/24/start
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560636-4473243792: 200
2025-07-07 10:22:40 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 10:22:40 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 10:22:40 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751858560678-4462665040: GET /api/scraping/sessions
2025-07-07 10:22:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751858560678-4462665040: 200
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751858562699-4462619472: GET /api/scraping/sessions
2025-07-07 10:22:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751858562699-4462619472: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563579-4473226000: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563581-4473250704: GET /api/scraping/sessions
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563583-4462622928: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563585-4473249104: GET /api/profiles
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563583-4462622928: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563585-4473249104: 307
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563579-4473226000: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563581-4473250704: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563629-4462474448: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751858563635-4473285200: GET /api/profiles/
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563629-4462474448: 200
2025-07-07 10:22:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751858563635-4473285200: 200
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751858565679-4462466576: GET /api/scraping/sessions
2025-07-07 10:22:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751858565679-4462466576: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567757-4473410576: GET /api/analytics/dashboard
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567762-4473297040: GET /api/campaigns
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567762-4473297040: 307
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751858567774-4473254736: GET /api/campaigns/
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567774-4473254736: 200
2025-07-07 10:22:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751858567757-4473410576: 200
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570224-4473405968: GET /api/profiles
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570224-4473405968: 307
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751858570246-4473417936: GET /api/profiles/
2025-07-07 10:22:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751858570246-4473417936: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582795-4462471568: GET /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-status
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582795-4462471568: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582832-4473300496: OPTIONS /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582832-4473300496: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582870-4473426448: POST /api/profiles/a2bd3c8c-3741-489b-9d6a-1824e67aac8d/facebook-login-complete
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582870-4473426448: 200
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582901-4473432272: GET /api/profiles
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582901-4473432272: 307
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751858582932-4473472720: GET /api/profiles/
2025-07-07 10:23:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751858582932-4473472720: 200
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751858597332-4473479824: POST /api/profiles/16cc6849-c8fe-4eaf-b833-d90f8e8ebfce/facebook-login
2025-07-07 10:23:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751858597332-4473479824: 200
2025-07-07 10:27:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:28 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:28 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:28 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:28 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:27:55 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:27:55 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:27:55 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:27:56 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:27:56 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:27:56 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:27:56 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:28:51 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:28:51 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:28:51 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:28:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:28:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:28:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:28:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:29:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:29:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:29:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:29:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:29:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:29:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:29:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:30 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:30 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:30 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:31 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:30:42 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:30:42 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:30:42 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:30:43 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:30:43 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:30:43 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:30:43 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:31:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:31:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:31:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859090159-4646364944: POST /api/profiles/
2025-07-07 10:31:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859090159-4646364944: 500
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:32:15 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:32:15 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:16 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:32:16 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:32:16 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:32:16 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:32:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751859156308-4465319440: POST /api/profiles/
2025-07-07 10:32:36 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859156308-4465319440: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337695), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 32, 36, 337698), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751859185074-4466590352: POST /api/profiles/
2025-07-07 10:33:05 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859185074-4466590352: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78261), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 33, 5, 78264), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:33:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:33:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:33:35 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:33:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:33:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:33:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859278069-4430405712: POST /api/profiles/
2025-07-07 10:34:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751859278069-4430405712: 400
2025-07-07 10:35:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751859304473-4431088784: POST /api/profiles/
2025-07-07 10:35:04 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859304473-4431088784: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478992), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 4, 478996), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:35:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751859341457-4432731344: POST /api/profiles/
2025-07-07 10:35:41 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859341457-4432731344: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462525), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 35, 41, 462531), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:08 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:08 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:09 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:09 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:09 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:09 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:36:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859386580-4436366992: POST /api/profiles/
2025-07-07 10:36:26 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751859386580-4436366992: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609720), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 36, 26, 609724), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:36:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751859409997-4437472528: GET /api/profiles/
2025-07-07 10:36:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751859409997-4437472528: 200
2025-07-07 10:36:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751859417887-4437677904: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:36:58 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:36:58 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:36:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:36:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:36:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:36:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:00 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:00 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:00 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:00 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:00 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:00 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:37:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751859417887-4437677904: 200
2025-07-07 10:37:02 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:37:02 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:37:02 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:37:03 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:37:03 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:37:03 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:37:03 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:20 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:20 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:21 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:21 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:21 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:21 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859511763-4714570192: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:38:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:38:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751859511763-4714570192: 200
2025-07-07 10:38:33 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:38:33 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:38:33 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:38:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:38:35 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:38:35 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:38:35 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751859543456-4509066064: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751859543456-4509066064: 200
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751859549449-4509741328: GET /api/profiles/browser-sessions
2025-07-07 10:39:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751859549449-4509741328: 500
2025-07-07 10:39:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751859566828-4509741840: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:39:27 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:27 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:27 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:27 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:27 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:27 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751859566828-4509741840: 200
2025-07-07 10:39:28 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:39:28 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:39:28 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:39:29 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:39:29 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:39:29 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:39:29 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751859570903-4606598288: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:39:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751859570903-4606598288: 200
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751859611980-4607212304: GET /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/browser-session-status
2025-07-07 10:40:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751859611980-4607212304: 200
2025-07-07 10:42:56 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:56 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:56 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:42:57 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:42:57 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:57 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:57 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:57 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:42:58 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:42:58 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:42:58 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:42:58 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:32 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:33 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:33 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:33 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:33 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751859825615-4436698640: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:43:45 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:45 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:45 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:43:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751859825615-4436698640: 200
2025-07-07 10:43:47 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:43:47 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:43:47 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:43:48 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:43:48 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:43:48 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:43:48 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751859878991-4596082128: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/facebook-login
2025-07-07 10:44:39 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:39 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:39 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:40 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:40 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:40 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:40 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751859878991-4596082128: 200
2025-07-07 10:44:41 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:44:41 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:44:41 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:44:42 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:44:42 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:44:42 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:44:42 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751859884077-4648463248: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:44:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751859884077-4648463248: 200
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751859922507-4649156240: POST /api/profiles/5ce43e09-6d68-43ed-8113-bb0275d5f921/terminate-browser-session
2025-07-07 10:45:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751859922507-4649156240: 200
2025-07-07 10:46:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:46:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:46:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:46:24 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:46:24 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:46:24 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:46:24 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991185-4722943568: GET /api/profiles
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991185-4722943568: 307
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751859991249-4723053776: GET /api/profiles/
2025-07-07 10:46:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751859991249-4723053776: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003841-4723668560: OPTIONS /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003841-4723668560: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003853-4723713296: DELETE /api/profiles/eec21405-f83d-41d5-ae37-c982a4102491
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003853-4723713296: 200
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003875-4723711184: GET /api/profiles
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003875-4723711184: 307
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751860003901-4722943376: GET /api/profiles/
2025-07-07 10:46:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751860003901-4722943376: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008638-4723711504: OPTIONS /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008638-4723711504: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008651-4722946896: DELETE /api/profiles/b5976381-7fb4-4394-8ea9-772a3457e7f6
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008651-4722946896: 200
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008669-4723779984: GET /api/profiles
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008669-4723779984: 307
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751860008692-4723776080: GET /api/profiles/
2025-07-07 10:46:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751860008692-4723776080: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023257-4723802192: OPTIONS /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023257-4723802192: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023284-4723804752: POST /api/profiles
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023284-4723804752: 307
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023321-4724887632: OPTIONS /api/profiles/
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860023321-4724887632: 200
2025-07-07 10:47:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860023328-4723781584: POST /api/profiles/
2025-07-07 10:47:03 | ERROR | middleware.error_handler:dispatch:76 | Unhandled Exception 1751860023328-4723781584: 2 validation errors:
  {'type': 'string_type', 'loc': ('response', 'created_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338822), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}
  {'type': 'string_type', 'loc': ('response', 'updated_at'), 'msg': 'Input should be a valid string', 'input': datetime.datetime(2025, 7, 7, 3, 47, 3, 338827), 'url': 'https://errors.pydantic.dev/2.5/v/string_type'}

2025-07-07 10:47:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:47:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:47:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:48:46 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:48:46 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:48:46 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:48:46 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751860146155-4381579920: POST /api/profiles/
2025-07-07 10:49:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751860146155-4381579920: 201
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751860173297-4382803328: GET /api/profiles/
2025-07-07 10:49:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751860173297-4382803328: 200
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860182173-4381684112: POST /api/profiles/
2025-07-07 10:49:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860182173-4381684112: 201
2025-07-07 10:51:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751860279560-4382881776: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/facebook-login
2025-07-07 10:51:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751860279560-4382881776: 200
2025-07-07 10:51:21 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:51:21 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:51:21 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:51:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:51:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:51:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:51:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860300272-4639696304: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:51:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860300272-4639696304: 500
2025-07-07 10:52:23 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:23 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:23 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:23 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:23 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:23 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:34 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:34 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:34 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:34 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:34 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:34 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:52:50 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:52:50 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:52:50 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:52:50 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:52:50 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:52:50 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:01 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:01 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:01 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:01 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:01 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:01 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:11 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:11 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:11 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:12 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:12 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:12 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:12 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:22 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:22 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:38 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:38 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:38 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:39 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:39 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:39 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:39 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:53:52 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:53:52 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:53:52 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:53:52 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:53:52 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:53:52 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:05 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:54:05 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:54:05 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:54:06 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:54:06 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:54:06 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:54:06 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751860457109-4442616640: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751860457109-4442616640: 200
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751860464091-4443400704: PUT /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb
2025-07-07 10:54:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751860464091-4443400704: 200
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751860482448-4443475104: POST /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/test-proxy
2025-07-07 10:54:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751860482448-4443475104: 200
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751860487572-4443399456: GET /api/profiles/8f17c792-565c-48c2-84e4-f0845d5ea9bb/zendriver-config
2025-07-07 10:54:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751860487572-4443399456: 200
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751860503080-4395241536: POST /api/profiles/
2025-07-07 10:55:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751860503080-4395241536: 201
2025-07-07 10:57:22 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:22 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:22 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:22 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648831-4417764368: GET /api/profiles
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648831-4417764368: 307
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751860648883-4417858128: GET /api/profiles/
2025-07-07 10:57:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751860648883-4417858128: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660480-4418502352: OPTIONS /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660480-4418502352: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660491-4418547280: DELETE /api/profiles/1f79c778-1a85-4aa5-a1d4-d3d17ead65dc
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660491-4418547280: 200
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660513-4418493328: GET /api/profiles
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660513-4418493328: 307
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751860660540-4417849168: GET /api/profiles/
2025-07-07 10:57:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751860660540-4417849168: 200
2025-07-07 10:57:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751860672560-4418588496: POST /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad/facebook-login
2025-07-07 10:57:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751860672560-4418588496: 200
2025-07-07 10:57:54 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 10:57:54 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 10:57:54 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 10:57:55 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 10:57:55 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 10:57:55 | INFO | main:lifespan:67 | Database initialized
2025-07-07 10:57:55 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:09:58 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398402-4659033104: GET /api/profiles
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398402-4659033104: 307
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861398466-4659110288: GET /api/profiles/
2025-07-07 11:09:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861398466-4659110288: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408421-4659762128: OPTIONS /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408421-4659762128: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408433-4659667664: PUT /api/profiles/16a85c57-4cb0-476d-86d5-00dbfe4585ad
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408433-4659667664: 200
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408468-4659808080: GET /api/profiles
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408468-4659808080: 307
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861408510-4659766416: GET /api/profiles/
2025-07-07 11:10:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861408510-4659766416: 200
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411624-4659819408: GET /api/profiles
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411624-4659819408: 307
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861411640-4659761616: GET /api/profiles/
2025-07-07 11:10:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861411640-4659761616: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425348-4659846672: OPTIONS /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425348-4659846672: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425365-4659851024: POST /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425365-4659851024: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425376-4660959568: OPTIONS /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425376-4660959568: 200
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425386-4659852816: POST /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425386-4659852816: 201
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425406-4660998672: GET /api/profiles
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425406-4660998672: 307
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861425458-4661090960: GET /api/profiles/
2025-07-07 11:10:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861425458-4661090960: 200
2025-07-07 11:10:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861428903-4660967312: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login
2025-07-07 11:10:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861428903-4660967312: 200
2025-07-07 11:10:31 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:10:31 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:10:31 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:10:32 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:10:32 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:10:32 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:10:32 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:11:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861511246-4401315024: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-terminate
2025-07-07 11:11:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861511246-4401315024: 404
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522876-4401412176: GET /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-status
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522876-4401412176: 200
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522964-4402043536: OPTIONS /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-complete
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522964-4402043536: 200
2025-07-07 11:12:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861522970-4402088464: POST /api/profiles/365da365-7035-4324-96c5-8999414f5ede/facebook-login-complete
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861522970-4402088464: 200
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861523023-4402091088: GET /api/profiles
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861523023-4402091088: 307
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861523058-4402100368: GET /api/profiles/
2025-07-07 11:12:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861523058-4402100368: 200
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861528859-4402138640: GET /api/scraping/sessions
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861528864-4401413072: GET /api/scraping/sessions
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861528859-4402138640: 200
2025-07-07 11:12:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861528864-4401413072: 200
2025-07-07 11:12:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861530927-4402099216: GET /api/scraping/sessions
2025-07-07 11:12:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861530927-4402099216: 200
2025-07-07 11:12:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861532976-4402204880: GET /api/scraping/sessions
2025-07-07 11:12:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861532976-4402204880: 200
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861534955-4402138896: OPTIONS /api/scraping/sessions/24
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861534955-4402138896: 200
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861534972-4402087824: DELETE /api/scraping/sessions/24
2025-07-07 11:12:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861534972-4402087824: 200
2025-07-07 11:12:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861535000-4402161808: GET /api/scraping/sessions
2025-07-07 11:12:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861535000-4402161808: 200
2025-07-07 11:12:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861537029-4402350160: GET /api/scraping/sessions
2025-07-07 11:12:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861537029-4402350160: 200
2025-07-07 11:12:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861539082-4402094160: GET /api/scraping/sessions
2025-07-07 11:12:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861539082-4402094160: 200
2025-07-07 11:12:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861541129-4402348560: GET /api/scraping/sessions
2025-07-07 11:12:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861541129-4402348560: 200
2025-07-07 11:12:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861543179-4402165456: GET /api/scraping/sessions
2025-07-07 11:12:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861543179-4402165456: 200
2025-07-07 11:12:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861545226-4402358160: GET /api/scraping/sessions
2025-07-07 11:12:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861545226-4402358160: 200
2025-07-07 11:12:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861547271-4402141968: GET /api/scraping/sessions
2025-07-07 11:12:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861547271-4402141968: 200
2025-07-07 11:12:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861549318-4402362000: GET /api/scraping/sessions
2025-07-07 11:12:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861549318-4402362000: 200
2025-07-07 11:12:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861551364-4402349968: GET /api/scraping/sessions
2025-07-07 11:12:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861551364-4402349968: 200
2025-07-07 11:12:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861553409-4402211216: GET /api/scraping/sessions
2025-07-07 11:12:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861553409-4402211216: 200
2025-07-07 11:12:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861555457-4402152720: GET /api/scraping/sessions
2025-07-07 11:12:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861555457-4402152720: 200
2025-07-07 11:12:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861557503-4402363728: GET /api/scraping/sessions
2025-07-07 11:12:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861557503-4402363728: 200
2025-07-07 11:12:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861559554-4402167376: GET /api/scraping/sessions
2025-07-07 11:12:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861559554-4402167376: 200
2025-07-07 11:12:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861561599-4402362000: GET /api/scraping/sessions
2025-07-07 11:12:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861561599-4402362000: 200
2025-07-07 11:12:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861563647-4402352464: GET /api/scraping/sessions
2025-07-07 11:12:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861563647-4402352464: 200
2025-07-07 11:12:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861565695-4402163536: GET /api/scraping/sessions
2025-07-07 11:12:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861565695-4402163536: 200
2025-07-07 11:12:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861567740-4402348688: GET /api/scraping/sessions
2025-07-07 11:12:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861567740-4402348688: 200
2025-07-07 11:12:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861569785-4402152208: GET /api/scraping/sessions
2025-07-07 11:12:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861569785-4402152208: 200
2025-07-07 11:12:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861571833-4402205328: GET /api/scraping/sessions
2025-07-07 11:12:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861571833-4402205328: 200
2025-07-07 11:12:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861573881-4402162128: GET /api/scraping/sessions
2025-07-07 11:12:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861573881-4402162128: 200
2025-07-07 11:12:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861575926-4402164880: GET /api/scraping/sessions
2025-07-07 11:12:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861575926-4402164880: 200
2025-07-07 11:12:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861577974-4402354768: GET /api/scraping/sessions
2025-07-07 11:12:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861577974-4402354768: 200
2025-07-07 11:13:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751861580022-4402162448: GET /api/scraping/sessions
2025-07-07 11:13:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751861580022-4402162448: 200
2025-07-07 11:13:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861582068-4402095312: GET /api/scraping/sessions
2025-07-07 11:13:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861582068-4402095312: 200
2025-07-07 11:13:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751861584107-4402161360: GET /api/scraping/sessions
2025-07-07 11:13:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751861584107-4402161360: 200
2025-07-07 11:13:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861586153-4402360912: GET /api/scraping/sessions
2025-07-07 11:13:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861586153-4402360912: 200
2025-07-07 11:13:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861588198-4402165200: GET /api/scraping/sessions
2025-07-07 11:13:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861588198-4402165200: 200
2025-07-07 11:13:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861590243-4402207184: GET /api/scraping/sessions
2025-07-07 11:13:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861590243-4402207184: 200
2025-07-07 11:13:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861592288-4402356176: GET /api/scraping/sessions
2025-07-07 11:13:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861592288-4402356176: 200
2025-07-07 11:13:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861594335-4402358800: GET /api/scraping/sessions
2025-07-07 11:13:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861594335-4402358800: 200
2025-07-07 11:13:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861596386-4401409360: GET /api/scraping/sessions
2025-07-07 11:13:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861596386-4401409360: 200
2025-07-07 11:13:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861598433-4402163344: GET /api/scraping/sessions
2025-07-07 11:13:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861598433-4402163344: 200
2025-07-07 11:13:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861600469-4402349136: GET /api/scraping/sessions
2025-07-07 11:13:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861600469-4402349136: 200
2025-07-07 11:13:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861602513-4402361616: GET /api/scraping/sessions
2025-07-07 11:13:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861602513-4402361616: 200
2025-07-07 11:13:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861604560-4402140432: GET /api/scraping/sessions
2025-07-07 11:13:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861604560-4402140432: 200
2025-07-07 11:13:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861606606-4402161168: GET /api/scraping/sessions
2025-07-07 11:13:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861606606-4402161168: 200
2025-07-07 11:13:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861608652-4402087504: GET /api/scraping/sessions
2025-07-07 11:13:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861608652-4402087504: 200
2025-07-07 11:13:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861610698-4402358352: GET /api/scraping/sessions
2025-07-07 11:13:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861610698-4402358352: 200
2025-07-07 11:13:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861612745-4399467472: GET /api/scraping/sessions
2025-07-07 11:13:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861612745-4399467472: 200
2025-07-07 11:13:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861614790-4402205648: GET /api/scraping/sessions
2025-07-07 11:13:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861614790-4402205648: 200
2025-07-07 11:13:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861616832-4402363600: GET /api/scraping/sessions
2025-07-07 11:13:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861616832-4402363600: 200
2025-07-07 11:13:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861618875-4402364112: GET /api/scraping/sessions
2025-07-07 11:13:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861618875-4402364112: 200
2025-07-07 11:13:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861620920-4402214800: GET /api/scraping/sessions
2025-07-07 11:13:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861620920-4402214800: 200
2025-07-07 11:13:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861622951-4402092048: GET /api/scraping/sessions
2025-07-07 11:13:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861622951-4402092048: 200
2025-07-07 11:13:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861624996-4399292496: GET /api/scraping/sessions
2025-07-07 11:13:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861624996-4399292496: 200
2025-07-07 11:13:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861627085-4402361232: GET /api/scraping/sessions
2025-07-07 11:13:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861627085-4402361232: 200
2025-07-07 11:13:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861629131-4402087824: GET /api/scraping/sessions
2025-07-07 11:13:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861629131-4402087824: 200
2025-07-07 11:13:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861631176-4402166352: GET /api/scraping/sessions
2025-07-07 11:13:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861631176-4402166352: 200
2025-07-07 11:13:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861633222-4399467216: GET /api/scraping/sessions
2025-07-07 11:13:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861633222-4399467216: 200
2025-07-07 11:13:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861635270-4402356688: GET /api/scraping/sessions
2025-07-07 11:13:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861635270-4402356688: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637301-4402203408: GET /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637301-4402203408: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637676-4402374544: OPTIONS /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637676-4402374544: 200
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637686-4402101584: POST /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637686-4402101584: 201
2025-07-07 11:13:57 | INFO | automation.performance_monitor:start_monitoring:492 | Performance monitoring started
2025-07-07 11:13:57 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:13:57 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861637727-4402363792: GET /api/scraping/sessions
2025-07-07 11:13:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861637727-4402363792: 200
2025-07-07 11:13:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861639748-4402444496: GET /api/scraping/sessions
2025-07-07 11:13:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861639748-4402444496: 200
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861641463-4402165200: POST /api/scraping/sessions/23/start
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861641463-4402165200: 200
2025-07-07 11:14:01 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:14:01 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861641492-4402373456: GET /api/scraping/sessions
2025-07-07 11:14:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861641492-4402373456: 200
2025-07-07 11:14:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861643512-4402433232: GET /api/scraping/sessions
2025-07-07 11:14:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861643512-4402433232: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645556-4402367248: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645556-4402367248: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645915-4402446224: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645929-4402430288: GET /api/scraping/sessions
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645932-4402376848: GET /api/profiles
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861645935-4402504912: GET /api/profiles
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645932-4402376848: 307
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645935-4402504912: 307
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645915-4402446224: 200
2025-07-07 11:14:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861645929-4402430288: 200
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861646028-4402374416: GET /api/profiles/
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861646032-4402508304: GET /api/profiles/
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861646032-4402508304: 200
2025-07-07 11:14:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861646028-4402374416: 200
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648034-4402507984: GET /api/scraping/sessions
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648034-4402507984: 200
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648182-4402634448: POST /api/scraping/sessions/23/start
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648182-4402634448: 200
2025-07-07 11:14:08 | INFO | automation.facebook_scraper_service:_scrape_facebook_post_uids_internal:131 | Starting Facebook UID scraping for post: https://www.facebook.com/groups/comailo/posts/617231614744189
2025-07-07 11:14:08 | ERROR | automation.browser_manager:launch_browser:63 | Profile configuration not found: default
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861648207-4402498768: GET /api/scraping/sessions
2025-07-07 11:14:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861648207-4402498768: 200
2025-07-07 11:14:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861650233-4402642640: GET /api/scraping/sessions
2025-07-07 11:14:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861650233-4402642640: 200
2025-07-07 11:14:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861652270-4402636880: GET /api/scraping/sessions
2025-07-07 11:14:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861652270-4402636880: 200
2025-07-07 11:14:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861654310-4402634320: GET /api/scraping/sessions
2025-07-07 11:14:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861654310-4402634320: 200
2025-07-07 11:14:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861656345-4402377552: GET /api/scraping/sessions
2025-07-07 11:14:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861656345-4402377552: 200
2025-07-07 11:14:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861658389-4402627920: GET /api/scraping/sessions
2025-07-07 11:14:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861658389-4402627920: 200
2025-07-07 11:14:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861660434-4399659728: GET /api/scraping/sessions
2025-07-07 11:14:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861660434-4399659728: 200
2025-07-07 11:14:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861662476-4402638288: GET /api/scraping/sessions
2025-07-07 11:14:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861662476-4402638288: 200
2025-07-07 11:14:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861664531-4402634064: GET /api/scraping/sessions
2025-07-07 11:14:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861664531-4402634064: 200
2025-07-07 11:14:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861666571-4402507856: GET /api/scraping/sessions
2025-07-07 11:14:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861666571-4402507856: 200
2025-07-07 11:14:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861668612-4402163216: GET /api/scraping/sessions
2025-07-07 11:14:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861668612-4402163216: 200
2025-07-07 11:14:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861670658-4402366864: GET /api/scraping/sessions
2025-07-07 11:14:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861670658-4402366864: 200
2025-07-07 11:14:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861672699-4402633488: GET /api/scraping/sessions
2025-07-07 11:14:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861672699-4402633488: 200
2025-07-07 11:14:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861674746-4402634000: GET /api/scraping/sessions
2025-07-07 11:14:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861674746-4402634000: 200
2025-07-07 11:14:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861676786-4402443600: GET /api/scraping/sessions
2025-07-07 11:14:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861676786-4402443600: 200
2025-07-07 11:14:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861678825-4402365648: GET /api/scraping/sessions
2025-07-07 11:14:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861678825-4402365648: 200
2025-07-07 11:14:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861680872-4402634256: GET /api/scraping/sessions
2025-07-07 11:14:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861680872-4402634256: 200
2025-07-07 11:14:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861682913-4402633040: GET /api/scraping/sessions
2025-07-07 11:14:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861682913-4402633040: 200
2025-07-07 11:14:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861684955-4402369616: GET /api/scraping/sessions
2025-07-07 11:14:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861684955-4402369616: 200
2025-07-07 11:14:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861686995-4402375568: GET /api/scraping/sessions
2025-07-07 11:14:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861686995-4402375568: 200
2025-07-07 11:14:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861689031-4402631248: GET /api/scraping/sessions
2025-07-07 11:14:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861689031-4402631248: 200
2025-07-07 11:14:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861691066-4402443216: GET /api/scraping/sessions
2025-07-07 11:14:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861691066-4402443216: 200
2025-07-07 11:14:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861693101-4402433168: GET /api/scraping/sessions
2025-07-07 11:14:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861693101-4402433168: 200
2025-07-07 11:14:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861695133-4402380368: GET /api/scraping/sessions
2025-07-07 11:14:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861695133-4402380368: 200
2025-07-07 11:14:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861697169-4402495632: GET /api/scraping/sessions
2025-07-07 11:14:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861697169-4402495632: 200
2025-07-07 11:14:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861699212-4402095312: GET /api/scraping/sessions
2025-07-07 11:14:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861699212-4402095312: 200
2025-07-07 11:15:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861701256-4402367504: GET /api/scraping/sessions
2025-07-07 11:15:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861701256-4402367504: 200
2025-07-07 11:15:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861703303-4402369616: GET /api/scraping/sessions
2025-07-07 11:15:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861703303-4402369616: 200
2025-07-07 11:15:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861705347-4402496784: GET /api/scraping/sessions
2025-07-07 11:15:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861705347-4402496784: 200
2025-07-07 11:15:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861707389-4402506896: GET /api/scraping/sessions
2025-07-07 11:15:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861707389-4402506896: 200
2025-07-07 11:15:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861709428-4402505040: GET /api/scraping/sessions
2025-07-07 11:15:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861709428-4402505040: 200
2025-07-07 11:15:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861711473-4401402448: GET /api/scraping/sessions
2025-07-07 11:15:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861711473-4401402448: 200
2025-07-07 11:15:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861713518-4402642320: GET /api/scraping/sessions
2025-07-07 11:15:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861713518-4402642320: 200
2025-07-07 11:15:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861715555-4402498512: GET /api/scraping/sessions
2025-07-07 11:15:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861715555-4402498512: 200
2025-07-07 11:15:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861717599-4402162576: GET /api/scraping/sessions
2025-07-07 11:15:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861717599-4402162576: 200
2025-07-07 11:15:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861719643-4402507536: GET /api/scraping/sessions
2025-07-07 11:15:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861719643-4402507536: 200
2025-07-07 11:15:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861721688-4402638864: GET /api/scraping/sessions
2025-07-07 11:15:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861721688-4402638864: 200
2025-07-07 11:15:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861723733-4402374224: GET /api/scraping/sessions
2025-07-07 11:15:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861723733-4402374224: 200
2025-07-07 11:15:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861725778-4402638864: GET /api/scraping/sessions
2025-07-07 11:15:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861725778-4402638864: 200
2025-07-07 11:15:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861727820-4402101968: GET /api/scraping/sessions
2025-07-07 11:15:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861727820-4402101968: 200
2025-07-07 11:15:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861729864-4402439504: GET /api/scraping/sessions
2025-07-07 11:15:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861729864-4402439504: 200
2025-07-07 11:15:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861731904-4402364688: GET /api/scraping/sessions
2025-07-07 11:15:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861731904-4402364688: 200
2025-07-07 11:15:33 | INFO | middleware.rate_limiter:_cleanup_old_data:194 | Cleaned up rate limiter data. Active clients: 1
2025-07-07 11:15:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861733948-4402629008: GET /api/scraping/sessions
2025-07-07 11:15:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861733948-4402629008: 200
2025-07-07 11:15:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861735989-4402152208: GET /api/scraping/sessions
2025-07-07 11:15:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861735989-4402152208: 200
2025-07-07 11:15:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861738032-4402442192: GET /api/scraping/sessions
2025-07-07 11:15:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861738032-4402442192: 200
2025-07-07 11:15:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861740076-4402432080: GET /api/scraping/sessions
2025-07-07 11:15:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861740076-4402432080: 200
2025-07-07 11:15:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861742121-4402437072: GET /api/scraping/sessions
2025-07-07 11:15:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861742121-4402437072: 200
2025-07-07 11:15:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861744160-4402361232: GET /api/scraping/sessions
2025-07-07 11:15:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861744160-4402361232: 200
2025-07-07 11:15:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861746198-4402437072: GET /api/scraping/sessions
2025-07-07 11:15:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861746198-4402437072: 200
2025-07-07 11:15:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861748238-4402444688: GET /api/scraping/sessions
2025-07-07 11:15:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861748238-4402444688: 200
2025-07-07 11:15:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861750271-4402638992: GET /api/scraping/sessions
2025-07-07 11:15:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861750271-4402638992: 200
2025-07-07 11:15:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861752315-4402437392: GET /api/scraping/sessions
2025-07-07 11:15:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861752315-4402437392: 200
2025-07-07 11:15:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861754358-4402502672: GET /api/scraping/sessions
2025-07-07 11:15:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861754358-4402502672: 200
2025-07-07 11:15:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751861756404-4402433808: GET /api/scraping/sessions
2025-07-07 11:15:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751861756404-4402433808: 200
2025-07-07 11:15:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861758460-4402634640: GET /api/scraping/sessions
2025-07-07 11:15:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861758460-4402634640: 200
2025-07-07 11:16:00 | INFO | middleware.error_handler:dispatch:23 | Request 1751861760504-4402630480: GET /api/scraping/sessions
2025-07-07 11:16:00 | INFO | middleware.error_handler:dispatch:38 | Response 1751861760504-4402630480: 200
2025-07-07 11:16:02 | INFO | middleware.error_handler:dispatch:23 | Request 1751861762551-4402161680: GET /api/scraping/sessions
2025-07-07 11:16:02 | INFO | middleware.error_handler:dispatch:38 | Response 1751861762551-4402161680: 200
2025-07-07 11:16:04 | INFO | middleware.error_handler:dispatch:23 | Request 1751861764593-4402190352: GET /api/scraping/sessions
2025-07-07 11:16:04 | INFO | middleware.error_handler:dispatch:38 | Response 1751861764593-4402190352: 200
2025-07-07 11:16:06 | INFO | middleware.error_handler:dispatch:23 | Request 1751861766634-4402635216: GET /api/scraping/sessions
2025-07-07 11:16:06 | INFO | middleware.error_handler:dispatch:38 | Response 1751861766634-4402635216: 200
2025-07-07 11:16:08 | INFO | middleware.error_handler:dispatch:23 | Request 1751861768678-4402629392: GET /api/scraping/sessions
2025-07-07 11:16:08 | INFO | middleware.error_handler:dispatch:38 | Response 1751861768678-4402629392: 200
2025-07-07 11:16:10 | INFO | middleware.error_handler:dispatch:23 | Request 1751861770721-4402363152: GET /api/scraping/sessions
2025-07-07 11:16:10 | INFO | middleware.error_handler:dispatch:38 | Response 1751861770721-4402363152: 200
2025-07-07 11:16:12 | INFO | middleware.error_handler:dispatch:23 | Request 1751861772764-4402641936: GET /api/scraping/sessions
2025-07-07 11:16:12 | INFO | middleware.error_handler:dispatch:38 | Response 1751861772764-4402641936: 200
2025-07-07 11:16:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861774807-4402371088: GET /api/scraping/sessions
2025-07-07 11:16:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861774807-4402371088: 200
2025-07-07 11:16:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861776845-4402641936: GET /api/scraping/sessions
2025-07-07 11:16:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861776845-4402641936: 200
2025-07-07 11:16:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861778881-4402628112: GET /api/scraping/sessions
2025-07-07 11:16:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861778881-4402628112: 200
2025-07-07 11:16:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861780918-4402636816: GET /api/scraping/sessions
2025-07-07 11:16:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861780918-4402636816: 200
2025-07-07 11:16:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861782962-4402432080: GET /api/scraping/sessions
2025-07-07 11:16:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861782962-4402432080: 200
2025-07-07 11:16:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861785005-4402637840: GET /api/scraping/sessions
2025-07-07 11:16:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861785005-4402637840: 200
2025-07-07 11:16:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861787048-4402626640: GET /api/scraping/sessions
2025-07-07 11:16:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861787048-4402626640: 200
2025-07-07 11:16:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861789088-4402633872: GET /api/scraping/sessions
2025-07-07 11:16:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861789088-4402633872: 200
2025-07-07 11:16:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861791124-4402368336: GET /api/scraping/sessions
2025-07-07 11:16:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861791124-4402368336: 200
2025-07-07 11:16:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861793176-4402366352: GET /api/scraping/sessions
2025-07-07 11:16:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861793176-4402366352: 200
2025-07-07 11:16:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861795219-4402352144: GET /api/scraping/sessions
2025-07-07 11:16:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861795219-4402352144: 200
2025-07-07 11:16:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861797263-4402435920: GET /api/scraping/sessions
2025-07-07 11:16:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861797263-4402435920: 200
2025-07-07 11:16:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861799301-4402629264: GET /api/scraping/sessions
2025-07-07 11:16:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861799301-4402629264: 200
2025-07-07 11:16:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861801348-4402367888: GET /api/scraping/sessions
2025-07-07 11:16:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861801348-4402367888: 200
2025-07-07 11:16:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861803377-4402365712: GET /api/scraping/sessions
2025-07-07 11:16:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861803377-4402365712: 200
2025-07-07 11:16:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861805414-4402510032: GET /api/scraping/sessions
2025-07-07 11:16:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861805414-4402510032: 200
2025-07-07 11:16:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861807461-4402631248: GET /api/scraping/sessions
2025-07-07 11:16:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861807461-4402631248: 200
2025-07-07 11:16:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861809503-4402141456: GET /api/scraping/sessions
2025-07-07 11:16:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861809503-4402141456: 200
2025-07-07 11:16:51 | INFO | middleware.error_handler:dispatch:23 | Request 1751861811539-4402378064: GET /api/scraping/sessions
2025-07-07 11:16:51 | INFO | middleware.error_handler:dispatch:38 | Response 1751861811539-4402378064: 200
2025-07-07 11:16:53 | INFO | middleware.error_handler:dispatch:23 | Request 1751861813585-4402046864: GET /api/scraping/sessions
2025-07-07 11:16:53 | INFO | middleware.error_handler:dispatch:38 | Response 1751861813585-4402046864: 200
2025-07-07 11:16:55 | INFO | middleware.error_handler:dispatch:23 | Request 1751861815621-4402507536: GET /api/scraping/sessions
2025-07-07 11:16:55 | INFO | middleware.error_handler:dispatch:38 | Response 1751861815621-4402507536: 200
2025-07-07 11:16:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861817663-4402099088: GET /api/scraping/sessions
2025-07-07 11:16:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861817663-4402099088: 200
2025-07-07 11:16:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861819709-4402631504: GET /api/scraping/sessions
2025-07-07 11:16:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861819709-4402631504: 200
2025-07-07 11:17:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861821753-4402369488: GET /api/scraping/sessions
2025-07-07 11:17:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861821753-4402369488: 200
2025-07-07 11:17:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861823795-4402638928: GET /api/scraping/sessions
2025-07-07 11:17:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861823795-4402638928: 200
2025-07-07 11:17:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861825836-4402637392: GET /api/scraping/sessions
2025-07-07 11:17:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861825836-4402637392: 200
2025-07-07 11:17:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861827872-4402371536: GET /api/scraping/sessions
2025-07-07 11:17:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861827872-4402371536: 200
2025-07-07 11:17:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861829917-4401085520: GET /api/scraping/sessions
2025-07-07 11:17:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861829917-4401085520: 200
2025-07-07 11:17:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861831961-4402629008: GET /api/scraping/sessions
2025-07-07 11:17:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861831961-4402629008: 200
2025-07-07 11:17:14 | INFO | middleware.error_handler:dispatch:23 | Request 1751861834001-4402627216: GET /api/scraping/sessions
2025-07-07 11:17:14 | INFO | middleware.error_handler:dispatch:38 | Response 1751861834001-4402627216: 200
2025-07-07 11:17:16 | INFO | middleware.error_handler:dispatch:23 | Request 1751861836045-4402372112: GET /api/scraping/sessions
2025-07-07 11:17:16 | INFO | middleware.error_handler:dispatch:38 | Response 1751861836045-4402372112: 200
2025-07-07 11:17:18 | INFO | middleware.error_handler:dispatch:23 | Request 1751861838089-4402440976: GET /api/scraping/sessions
2025-07-07 11:17:18 | INFO | middleware.error_handler:dispatch:38 | Response 1751861838089-4402440976: 200
2025-07-07 11:17:20 | INFO | middleware.error_handler:dispatch:23 | Request 1751861840149-4402378000: GET /api/scraping/sessions
2025-07-07 11:17:20 | INFO | middleware.error_handler:dispatch:38 | Response 1751861840149-4402378000: 200
2025-07-07 11:17:22 | INFO | middleware.error_handler:dispatch:23 | Request 1751861842217-4398835152: GET /api/scraping/sessions
2025-07-07 11:17:22 | INFO | middleware.error_handler:dispatch:38 | Response 1751861842217-4398835152: 200
2025-07-07 11:17:24 | INFO | middleware.error_handler:dispatch:23 | Request 1751861844255-4402503568: GET /api/scraping/sessions
2025-07-07 11:17:24 | INFO | middleware.error_handler:dispatch:38 | Response 1751861844255-4402503568: 200
2025-07-07 11:17:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861846296-4402372176: GET /api/scraping/sessions
2025-07-07 11:17:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861846296-4402372176: 200
2025-07-07 11:17:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861848339-4402640656: GET /api/scraping/sessions
2025-07-07 11:17:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861848339-4402640656: 200
2025-07-07 11:17:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861850380-4402377040: GET /api/scraping/sessions
2025-07-07 11:17:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861850380-4402377040: 200
2025-07-07 11:17:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861852421-4402629264: GET /api/scraping/sessions
2025-07-07 11:17:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861852421-4402629264: 200
2025-07-07 11:17:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861854461-4402190352: GET /api/scraping/sessions
2025-07-07 11:17:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861854461-4402190352: 200
2025-07-07 11:17:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861856503-4402440336: GET /api/scraping/sessions
2025-07-07 11:17:36 | INFO | middleware.error_handler:dispatch:38 | Response 1751861856503-4402440336: 200
2025-07-07 11:17:38 | INFO | middleware.error_handler:dispatch:23 | Request 1751861858546-4402144208: GET /api/scraping/sessions
2025-07-07 11:17:38 | INFO | middleware.error_handler:dispatch:38 | Response 1751861858546-4402144208: 200
2025-07-07 11:17:40 | INFO | middleware.error_handler:dispatch:23 | Request 1751861860590-4402628048: GET /api/scraping/sessions
2025-07-07 11:17:40 | INFO | middleware.error_handler:dispatch:38 | Response 1751861860590-4402628048: 200
2025-07-07 11:17:42 | INFO | middleware.error_handler:dispatch:23 | Request 1751861862633-4402044112: GET /api/scraping/sessions
2025-07-07 11:17:42 | INFO | middleware.error_handler:dispatch:38 | Response 1751861862633-4402044112: 200
2025-07-07 11:17:44 | INFO | middleware.error_handler:dispatch:23 | Request 1751861864678-4402164944: GET /api/scraping/sessions
2025-07-07 11:17:44 | INFO | middleware.error_handler:dispatch:38 | Response 1751861864678-4402164944: 200
2025-07-07 11:17:46 | INFO | middleware.error_handler:dispatch:23 | Request 1751861866723-4402095504: GET /api/scraping/sessions
2025-07-07 11:17:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861866723-4402095504: 200
2025-07-07 11:17:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861868763-4402635920: GET /api/scraping/sessions
2025-07-07 11:17:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861868763-4402635920: 200
2025-07-07 11:17:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861870800-4402354896: GET /api/scraping/sessions
2025-07-07 11:17:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861870800-4402354896: 200
2025-07-07 11:17:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861872836-4402442960: GET /api/scraping/sessions
2025-07-07 11:17:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861872836-4402442960: 200
2025-07-07 11:17:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861874880-4402634576: GET /api/scraping/sessions
2025-07-07 11:17:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861874880-4402634576: 200
2025-07-07 11:17:56 | INFO | middleware.error_handler:dispatch:23 | Request 1751861876927-4402377936: GET /api/scraping/sessions
2025-07-07 11:17:56 | INFO | middleware.error_handler:dispatch:38 | Response 1751861876927-4402377936: 200
2025-07-07 11:17:58 | INFO | middleware.error_handler:dispatch:23 | Request 1751861878970-4402157712: GET /api/scraping/sessions
2025-07-07 11:17:58 | INFO | middleware.error_handler:dispatch:38 | Response 1751861878970-4402157712: 200
2025-07-07 11:18:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861881026-4402436496: GET /api/scraping/sessions
2025-07-07 11:18:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861881026-4402436496: 200
2025-07-07 11:18:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861883071-4402372496: GET /api/scraping/sessions
2025-07-07 11:18:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861883071-4402372496: 200
2025-07-07 11:18:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861885116-4402632528: GET /api/scraping/sessions
2025-07-07 11:18:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861885116-4402632528: 200
2025-07-07 11:18:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861887162-4401984720: GET /api/scraping/sessions
2025-07-07 11:18:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861887162-4401984720: 200
2025-07-07 11:18:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861889209-4402442320: GET /api/scraping/sessions
2025-07-07 11:18:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861889209-4402442320: 200
2025-07-07 11:18:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861891246-4402375440: GET /api/scraping/sessions
2025-07-07 11:18:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861891246-4402375440: 200
2025-07-07 11:18:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861893289-4402216848: GET /api/scraping/sessions
2025-07-07 11:18:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861893289-4402216848: 200
2025-07-07 11:18:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861895333-4402440976: GET /api/scraping/sessions
2025-07-07 11:18:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861895333-4402440976: 200
2025-07-07 11:18:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861897380-4402086672: GET /api/scraping/sessions
2025-07-07 11:18:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861897380-4402086672: 200
2025-07-07 11:18:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861899429-4402627792: GET /api/scraping/sessions
2025-07-07 11:18:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861899429-4402627792: 200
2025-07-07 11:18:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861901475-4402496976: GET /api/scraping/sessions
2025-07-07 11:18:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861901475-4402496976: 200
2025-07-07 11:18:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861903529-4402634192: GET /api/scraping/sessions
2025-07-07 11:18:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861903529-4402634192: 200
2025-07-07 11:18:25 | INFO | middleware.error_handler:dispatch:23 | Request 1751861905571-4402442384: GET /api/scraping/sessions
2025-07-07 11:18:25 | INFO | middleware.error_handler:dispatch:38 | Response 1751861905571-4402442384: 200
2025-07-07 11:18:27 | INFO | middleware.error_handler:dispatch:23 | Request 1751861907608-4402369296: GET /api/scraping/sessions
2025-07-07 11:18:27 | INFO | middleware.error_handler:dispatch:38 | Response 1751861907608-4402369296: 200
2025-07-07 11:18:29 | INFO | middleware.error_handler:dispatch:23 | Request 1751861909653-4402634704: GET /api/scraping/sessions
2025-07-07 11:18:29 | INFO | middleware.error_handler:dispatch:38 | Response 1751861909653-4402634704: 200
2025-07-07 11:18:31 | INFO | middleware.error_handler:dispatch:23 | Request 1751861911693-4402437392: GET /api/scraping/sessions
2025-07-07 11:18:31 | INFO | middleware.error_handler:dispatch:38 | Response 1751861911693-4402437392: 200
2025-07-07 11:18:33 | INFO | middleware.error_handler:dispatch:23 | Request 1751861913741-4402502096: GET /api/scraping/sessions
2025-07-07 11:18:33 | INFO | middleware.error_handler:dispatch:38 | Response 1751861913741-4402502096: 200
2025-07-07 11:18:35 | INFO | middleware.error_handler:dispatch:23 | Request 1751861915782-4402632848: GET /api/scraping/sessions
2025-07-07 11:18:35 | INFO | middleware.error_handler:dispatch:38 | Response 1751861915782-4402632848: 200
2025-07-07 11:18:37 | INFO | middleware.error_handler:dispatch:23 | Request 1751861917815-4402440976: GET /api/scraping/sessions
2025-07-07 11:18:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861917815-4402440976: 200
2025-07-07 11:18:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861919857-4402135696: GET /api/scraping/sessions
2025-07-07 11:18:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861919857-4402135696: 200
2025-07-07 11:18:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861921900-4402432080: GET /api/scraping/sessions
2025-07-07 11:18:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861921900-4402432080: 200
2025-07-07 11:18:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861923947-4402376912: GET /api/scraping/sessions
2025-07-07 11:18:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861923947-4402376912: 200
2025-07-07 11:18:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861925991-4402672784: GET /api/scraping/sessions
2025-07-07 11:18:46 | INFO | middleware.error_handler:dispatch:38 | Response 1751861925991-4402672784: 200
2025-07-07 11:18:48 | INFO | middleware.error_handler:dispatch:23 | Request 1751861928034-4402511440: GET /api/scraping/sessions
2025-07-07 11:18:48 | INFO | middleware.error_handler:dispatch:38 | Response 1751861928034-4402511440: 200
2025-07-07 11:18:50 | INFO | middleware.error_handler:dispatch:23 | Request 1751861930080-4402668944: GET /api/scraping/sessions
2025-07-07 11:18:50 | INFO | middleware.error_handler:dispatch:38 | Response 1751861930080-4402668944: 200
2025-07-07 11:18:52 | INFO | middleware.error_handler:dispatch:23 | Request 1751861932125-4402634640: GET /api/scraping/sessions
2025-07-07 11:18:52 | INFO | middleware.error_handler:dispatch:38 | Response 1751861932125-4402634640: 200
2025-07-07 11:18:54 | INFO | middleware.error_handler:dispatch:23 | Request 1751861934172-4402674960: GET /api/scraping/sessions
2025-07-07 11:18:54 | INFO | middleware.error_handler:dispatch:38 | Response 1751861934172-4402674960: 200
2025-07-07 11:18:56 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:18:56 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:18:56 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:18:57 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:18:57 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:18:57 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:18:57 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:18:57 | INFO | middleware.error_handler:dispatch:23 | Request 1751861937421-4567810448: GET /api/scraping/sessions
2025-07-07 11:18:57 | INFO | middleware.error_handler:dispatch:38 | Response 1751861937421-4567810448: 200
2025-07-07 11:18:59 | INFO | middleware.error_handler:dispatch:23 | Request 1751861939459-4568397328: GET /api/scraping/sessions
2025-07-07 11:18:59 | INFO | middleware.error_handler:dispatch:38 | Response 1751861939459-4568397328: 200
2025-07-07 11:19:01 | INFO | middleware.error_handler:dispatch:23 | Request 1751861941503-4568452752: GET /api/scraping/sessions
2025-07-07 11:19:01 | INFO | middleware.error_handler:dispatch:38 | Response 1751861941503-4568452752: 200
2025-07-07 11:19:03 | INFO | middleware.error_handler:dispatch:23 | Request 1751861943544-4568463184: GET /api/scraping/sessions
2025-07-07 11:19:03 | INFO | middleware.error_handler:dispatch:38 | Response 1751861943544-4568463184: 200
2025-07-07 11:19:05 | INFO | middleware.error_handler:dispatch:23 | Request 1751861945588-4567820496: GET /api/scraping/sessions
2025-07-07 11:19:05 | INFO | middleware.error_handler:dispatch:38 | Response 1751861945588-4567820496: 200
2025-07-07 11:19:07 | INFO | middleware.error_handler:dispatch:23 | Request 1751861947636-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:07 | INFO | middleware.error_handler:dispatch:38 | Response 1751861947636-4568482640: 200
2025-07-07 11:19:09 | INFO | middleware.error_handler:dispatch:23 | Request 1751861949669-4568458064: GET /api/scraping/sessions
2025-07-07 11:19:09 | INFO | middleware.error_handler:dispatch:38 | Response 1751861949669-4568458064: 200
2025-07-07 11:19:11 | INFO | middleware.error_handler:dispatch:23 | Request 1751861951707-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:11 | INFO | middleware.error_handler:dispatch:38 | Response 1751861951707-4568482640: 200
2025-07-07 11:19:13 | INFO | middleware.error_handler:dispatch:23 | Request 1751861953752-4567812304: GET /api/scraping/sessions
2025-07-07 11:19:13 | INFO | middleware.error_handler:dispatch:38 | Response 1751861953752-4567812304: 200
2025-07-07 11:19:15 | INFO | middleware.error_handler:dispatch:23 | Request 1751861955797-4568482640: GET /api/scraping/sessions
2025-07-07 11:19:15 | INFO | middleware.error_handler:dispatch:38 | Response 1751861955797-4568482640: 200
2025-07-07 11:19:17 | INFO | middleware.error_handler:dispatch:23 | Request 1751861957843-4568456656: GET /api/scraping/sessions
2025-07-07 11:19:17 | INFO | middleware.error_handler:dispatch:38 | Response 1751861957843-4568456656: 200
2025-07-07 11:19:19 | INFO | middleware.error_handler:dispatch:23 | Request 1751861959887-4568488016: GET /api/scraping/sessions
2025-07-07 11:19:19 | INFO | middleware.error_handler:dispatch:38 | Response 1751861959887-4568488016: 200
2025-07-07 11:19:21 | INFO | middleware.error_handler:dispatch:23 | Request 1751861961931-4568392592: GET /api/scraping/sessions
2025-07-07 11:19:21 | INFO | middleware.error_handler:dispatch:38 | Response 1751861961931-4568392592: 200
2025-07-07 11:19:23 | INFO | middleware.error_handler:dispatch:23 | Request 1751861963978-4568488400: GET /api/scraping/sessions
2025-07-07 11:19:23 | INFO | middleware.error_handler:dispatch:38 | Response 1751861963978-4568488400: 200
2025-07-07 11:19:26 | INFO | middleware.error_handler:dispatch:23 | Request 1751861966024-4567812048: GET /api/scraping/sessions
2025-07-07 11:19:26 | INFO | middleware.error_handler:dispatch:38 | Response 1751861966024-4567812048: 200
2025-07-07 11:19:28 | INFO | middleware.error_handler:dispatch:23 | Request 1751861968069-4568481872: GET /api/scraping/sessions
2025-07-07 11:19:28 | INFO | middleware.error_handler:dispatch:38 | Response 1751861968069-4568481872: 200
2025-07-07 11:19:29 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:19:29 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:19:29 | INFO | main:lifespan:80 | Database connections closed
2025-07-07 11:19:30 | INFO | main:lifespan:63 | Starting Facebook Automation App Backend...
2025-07-07 11:19:30 | INFO | app.core.database:init_database:137 | Database tables created successfully
2025-07-07 11:19:30 | INFO | main:lifespan:67 | Database initialized
2025-07-07 11:19:30 | INFO | main:lifespan:73 | Server starting on 127.0.0.1:8000
2025-07-07 11:19:30 | INFO | middleware.error_handler:dispatch:23 | Request 1751861970871-4471241360: GET /api/scraping/sessions
2025-07-07 11:19:30 | INFO | middleware.error_handler:dispatch:38 | Response 1751861970871-4471241360: 200
2025-07-07 11:19:32 | INFO | middleware.error_handler:dispatch:23 | Request 1751861972910-4471928208: GET /api/scraping/sessions
2025-07-07 11:19:32 | INFO | middleware.error_handler:dispatch:38 | Response 1751861972910-4471928208: 200
2025-07-07 11:19:34 | INFO | middleware.error_handler:dispatch:23 | Request 1751861974955-4471924432: GET /api/scraping/sessions
2025-07-07 11:19:34 | INFO | middleware.error_handler:dispatch:38 | Response 1751861974955-4471924432: 200
2025-07-07 11:19:36 | INFO | middleware.error_handler:dispatch:23 | Request 1751861976989-4471994000: GET /api/scraping/sessions
2025-07-07 11:19:37 | INFO | middleware.error_handler:dispatch:38 | Response 1751861976989-4471994000: 200
2025-07-07 11:19:39 | INFO | middleware.error_handler:dispatch:23 | Request 1751861979032-4471347600: GET /api/scraping/sessions
2025-07-07 11:19:39 | INFO | middleware.error_handler:dispatch:38 | Response 1751861979032-4471347600: 200
2025-07-07 11:19:41 | INFO | middleware.error_handler:dispatch:23 | Request 1751861981079-4472014992: GET /api/scraping/sessions
2025-07-07 11:19:41 | INFO | middleware.error_handler:dispatch:38 | Response 1751861981079-4472014992: 200
2025-07-07 11:19:43 | INFO | middleware.error_handler:dispatch:23 | Request 1751861983117-4465082576: GET /api/scraping/sessions
2025-07-07 11:19:43 | INFO | middleware.error_handler:dispatch:38 | Response 1751861983117-4465082576: 200
2025-07-07 11:19:45 | INFO | middleware.error_handler:dispatch:23 | Request 1751861985163-4472016656: GET /api/scraping/sessions
2025-07-07 11:19:45 | INFO | middleware.error_handler:dispatch:38 | Response 1751861985163-4472016656: 200
2025-07-07 11:19:47 | INFO | middleware.error_handler:dispatch:23 | Request 1751861987200-4471241488: GET /api/scraping/sessions
2025-07-07 11:19:47 | INFO | middleware.error_handler:dispatch:38 | Response 1751861987200-4471241488: 200
2025-07-07 11:19:49 | INFO | middleware.error_handler:dispatch:23 | Request 1751861989234-4472019344: GET /api/scraping/sessions
2025-07-07 11:19:49 | INFO | middleware.error_handler:dispatch:38 | Response 1751861989234-4472019344: 200
2025-07-07 11:19:50 | INFO | main:lifespan:78 | Shutting down...
2025-07-07 11:19:50 | INFO | app.core.database:close_database:148 | Database connections closed
2025-07-07 11:19:50 | INFO | main:lifespan:80 | Database connections closed

"""
Zendriver service for managing antidetect browser sessions.
"""
import asyncio
import json
import os
import subprocess
import sys
import time
from typing import Dict, Any, Optional, List
import logging

from ..models.profile import Profile
from .browser_service import BrowserService

logger = logging.getLogger(__name__)


class ZendriverService:
    """Service for managing zendriver browser sessions."""
    
    def __init__(self):
        self.browser_service = BrowserService()
        self.active_sessions = {}  # profile_id -> session_info
        self.session_timeout = 3600  # 1 hour
        
    async def launch_browser_for_facebook_login(self, profile: Profile) -> Dict[str, Any]:
        """Launch antidetect browser using zendriver for Facebook login."""
        try:
            # Check if session already exists
            if profile.id in self.active_sessions:
                session_info = self.active_sessions[profile.id]
                if self._is_session_active(session_info):
                    return {
                        "status": "session_exists",
                        "message": "Browser session already active for this profile",
                        "session_id": session_info["session_id"]
                    }
                else:
                    # Clean up expired session
                    await self._cleanup_session(profile.id)
            
            # Get zendriver configuration
            zendriver_config = self.browser_service.get_zendriver_config(profile)
            
            # Create session directory
            session_dir = self._get_session_directory(profile)
            os.makedirs(session_dir, exist_ok=True)
            
            # Create zendriver script
            script_path = os.path.join(session_dir, "facebook_login.py")
            self._create_zendriver_script(script_path, zendriver_config, profile)
            
            # Launch browser process
            process = await self._launch_browser_process(script_path, session_dir)
            
            # Store session info
            session_info = {
                "session_id": f"fb_login_{profile.id}_{int(time.time())}",
                "profile_id": profile.id,
                "process": process,
                "script_path": script_path,
                "session_dir": session_dir,
                "started_at": time.time(),
                "status": "active"
            }
            
            self.active_sessions[profile.id] = session_info
            
            logger.info(f"Browser launched for Facebook login - Profile: {profile.name} ({profile.id})")
            
            return {
                "status": "browser_launched",
                "message": "Antidetect browser launched successfully. Complete Facebook login manually.",
                "session_id": session_info["session_id"],
                "instructions": self._get_login_instructions()
            }
            
        except Exception as e:
            logger.error(f"Error launching browser for profile {profile.id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to launch browser: {str(e)}"
            }
    
    def _create_zendriver_script(self, script_path: str, zendriver_config: Dict, profile: Profile):
        """Create Python script for zendriver browser automation."""
        # Convert config to proper Python format
        config_str = json.dumps(zendriver_config, indent=8).replace('true', 'True').replace('false', 'False').replace('null', 'None')

        script_content = f'''
import asyncio
import sys
import os
import json
import time

# Add zendriver to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "../../zendriver_local"))

try:
    import zendriver as zd
except ImportError as e:
    print(f"Error importing zendriver: {{e}}")
    print("Make sure zendriver is installed and accessible")
    sys.exit(1)

async def main():
    """Launch antidetect browser for Facebook login."""
    try:
        print("Starting antidetect browser for Facebook login...")
        print("Profile: {profile.name} (ID: {profile.id})")

        # Browser configuration with antidetect settings
        config = {config_str}

        # Start browser with antidetect configuration
        browser = await zd.start(**config)
        print("Browser started successfully")

        # Navigate to Facebook login
        page = await browser.get("https://www.facebook.com/login")
        print("Navigated to Facebook login page")

        # Set page title to identify the session
        await page.evaluate("""
            document.title = 'Facebook Login - {profile.name} - Complete login manually then click Complete Login in app';
        """)

        print("\\n" + "="*60)
        print("FACEBOOK LOGIN INSTRUCTIONS:")
        print("1. Enter your Facebook email/phone and password")
        print("2. Complete any 2FA verification if prompted")
        print("3. Solve any CAPTCHA or security challenges")
        print("4. Once logged in, click 'Complete Login' in the app")
        print("="*60 + "\\n")

        # Keep browser open for manual login
        # Browser will stay open until user completes login or timeout
        session_file = os.path.join(os.path.dirname(__file__), "session_status.json")

        start_time = time.time()
        timeout = 3600  # 1 hour

        while time.time() - start_time < timeout:
            # Check if session should be terminated
            if os.path.exists(session_file):
                with open(session_file, 'r') as f:
                    status = json.load(f)
                    if status.get('action') == 'terminate':
                        print("Session termination requested")
                        break

            # Check if user is logged in by looking for Facebook elements
            try:
                # Check if we're on Facebook homepage or logged in
                current_url = page.url
                if 'facebook.com' in current_url and '/login' not in current_url:
                    print("Login detected! User appears to be logged in.")
                    # Update session status
                    with open(session_file, 'w') as f:
                        json.dump({{
                            'status': 'logged_in',
                            'url': current_url,
                            'timestamp': time.time()
                        }}, f)
            except Exception as e:
                print(f"Error checking login status: {{e}}")

            await asyncio.sleep(5)  # Check every 5 seconds

        print("Session ending...")
        await browser.stop()

    except Exception as e:
        print(f"Error in browser session: {{e}}")
        import traceback
        traceback.print_exc()

    print("Browser session ended")

if __name__ == "__main__":
    asyncio.run(main())
'''

        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
    
    async def _launch_browser_process(self, script_path: str, session_dir: str) -> subprocess.Popen:
        """Launch the browser process."""
        # Use Python to run the zendriver script
        cmd = [sys.executable, script_path]
        
        # Launch process with error handling
        try:
            process = subprocess.Popen(
                cmd,
                cwd=session_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # Give it a moment to start
            await asyncio.sleep(2)

            # Check if process is still running
            if process.poll() is not None:
                # Process has already terminated
                stdout, stderr = process.communicate()
                logger.error(f"Browser process terminated immediately. stdout: {stdout}, stderr: {stderr}")
                raise Exception(f"Browser process failed to start: {stderr}")

            return process

        except Exception as e:
            logger.error(f"Failed to launch browser process: {str(e)}")
            raise Exception(f"Failed to launch browser: {str(e)}")
    
    def _get_session_directory(self, profile: Profile) -> str:
        """Get session directory for profile."""
        base_dir = os.path.join(os.getcwd(), "browser_sessions")
        return os.path.join(base_dir, f"profile_{profile.id}")
    
    def _is_session_active(self, session_info: Dict) -> bool:
        """Check if browser session is still active."""
        if not session_info:
            return False
            
        # Check if process is still running
        process = session_info.get("process")
        if process and process.poll() is None:
            # Check timeout
            elapsed = time.time() - session_info.get("started_at", 0)
            return elapsed < self.session_timeout
        
        return False
    
    async def _cleanup_session(self, profile_id: str):
        """Clean up browser session."""
        if profile_id in self.active_sessions:
            session_info = self.active_sessions[profile_id]
            
            # Terminate process if still running
            process = session_info.get("process")
            if process and process.poll() is None:
                try:
                    process.terminate()
                    await asyncio.sleep(1)
                    if process.poll() is None:
                        process.kill()
                except Exception as e:
                    logger.error(f"Error terminating process: {e}")
            
            # Remove from active sessions
            del self.active_sessions[profile_id]
            logger.info(f"Cleaned up session for profile {profile_id}")
    
    async def terminate_session(self, profile_id: str) -> Dict[str, Any]:
        """Terminate browser session for profile."""
        try:
            if profile_id not in self.active_sessions:
                return {
                    "status": "no_session",
                    "message": "No active session found for this profile"
                }
            
            session_info = self.active_sessions[profile_id]
            
            # Signal session to terminate
            session_file = os.path.join(session_info["session_dir"], "session_status.json")
            with open(session_file, 'w') as f:
                json.dump({"action": "terminate", "timestamp": time.time()}, f)
            
            # Clean up session
            await self._cleanup_session(profile_id)
            
            return {
                "status": "terminated",
                "message": "Browser session terminated successfully"
            }
            
        except Exception as e:
            logger.error(f"Error terminating session for profile {profile_id}: {str(e)}")
            return {
                "status": "error",
                "message": f"Failed to terminate session: {str(e)}"
            }
    
    def get_session_status(self, profile_id: str) -> Dict[str, Any]:
        """Get status of browser session."""
        if profile_id not in self.active_sessions:
            return {
                "status": "no_session",
                "message": "No active session found"
            }
        
        session_info = self.active_sessions[profile_id]
        
        if self._is_session_active(session_info):
            elapsed = time.time() - session_info.get("started_at", 0)
            return {
                "status": "active",
                "session_id": session_info["session_id"],
                "elapsed_time": elapsed,
                "timeout_remaining": max(0, self.session_timeout - elapsed)
            }
        else:
            return {
                "status": "expired",
                "message": "Session has expired or browser was closed"
            }
    
    def _get_login_instructions(self) -> List[str]:
        """Get step-by-step login instructions."""
        return [
            "1. Browser window will open with your profile configuration",
            "2. Navigate to Facebook login page (already done)",
            "3. Enter your Facebook credentials manually",
            "4. Complete any 2FA or security checks",
            "5. Once logged in, click 'Complete Login' button in the app"
        ]

    def get_all_active_sessions(self) -> Dict[str, Any]:
        """Get information about all active browser sessions."""
        active_sessions = {}

        for profile_id, session_info in self.active_sessions.items():
            if self._is_session_active(session_info):
                elapsed = time.time() - session_info.get("started_at", 0)
                active_sessions[profile_id] = {
                    "session_id": session_info["session_id"],
                    "status": "active",
                    "elapsed_time": elapsed,
                    "timeout_remaining": max(0, self.session_timeout - elapsed)
                }

        return {
            "total_active_sessions": len(active_sessions),
            "sessions": active_sessions
        }


# Global instance
zendriver_service = ZendriverService()

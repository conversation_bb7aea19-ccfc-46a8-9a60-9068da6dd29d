{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462508803269", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 26589}, "server": "https://scontent-hkg4-2.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398968188932818", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "network_stats": {"srtt": 25523}, "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462598848094", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 27486}, "server": "https://scontent-hkg1-1.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462598916187", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 26098}, "server": "https://scontent-hkg4-1.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398968214411953", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 29024}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462632419047", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 47273}, "server": "https://facebook.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462633457788", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 47273}, "server": "https://www.facebook.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462598900127", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 26313}, "server": "https://scontent-hkg1-2.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462598899881", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 26313}, "server": "https://scontent.xx.fbcdn.net"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13396462627044712", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABQAAABodHRwczovL2ZhY2Vib29rLmNvbQ==", false, 0], "network_stats": {"srtt": 30520}, "server": "https://static.xx.fbcdn.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398968242990712", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://www.gstatic.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "server": "https://apis.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 38226}, "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 29123}, "server": "https://ogads-pa.clients6.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABUAAABjaHJvbWU6Ly9uZXctdGFiLXBhZ2UAAAA=", true, 0], "network_stats": {"srtt": 29777}, "server": "https://play.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 25666}, "server": "https://www.google.com"}], "supports_quic": {"address": "2606:4700:110:8041:7f54:ade7:c537:ff6a", "used_quic": true}, "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}
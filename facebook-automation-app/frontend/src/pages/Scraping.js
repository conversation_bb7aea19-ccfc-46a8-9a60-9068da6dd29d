import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Card,
  CardContent,
  Button,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,

  IconButton,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  Tabs,
  Tab,
  Avatar,
  Divider,

  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Download as DownloadIcon,
  MoreVert as MoreVertIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Facebook as FacebookIcon,
  Comment as CommentIcon,
  ThumbUp as ThumbUpIcon,
  Share as ShareIcon,

  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { scrapingAPI, profilesAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';
import TabPanel from '../components/Common/TabPanel';

function ScrapingSessionCard({ session, onStart, onStop, onView, onDelete, onExport }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running':
        return 'info';
      case 'pending':
        return 'warning';
      case 'completed':
        return 'success';
      case 'failed':
      case 'error':
        return 'error';
      case 'paused':
      case 'stopped':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'running':
        return <CircularProgress size={16} />;
      case 'pending':
        return <ScheduleIcon fontSize="small" />;
      case 'completed':
        return <CheckCircleIcon fontSize="small" />;
      case 'failed':
      case 'error':
        return <ErrorIcon fontSize="small" />;
      case 'paused':
      case 'stopped':
        return <ScheduleIcon fontSize="small" />;
      default:
        return null;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              <FacebookIcon />
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {session.name}
              </Typography>
              <Chip
                icon={getStatusIcon(session.status)}
                label={session.status || 'draft'}
                size="small"
                color={getStatusColor(session.status)}
              />
            </Box>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {session.post_url}
        </Typography>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Types
            </Typography>
            <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
              {session.scraping_types?.includes('comments') && (
                <Chip icon={<CommentIcon />} label="Comments" size="small" />
              )}
              {session.scraping_types?.includes('likes') && (
                <Chip icon={<ThumbUpIcon />} label="Likes" size="small" />
              )}
              {session.scraping_types?.includes('shares') && (
                <Chip icon={<ShareIcon />} label="Shares" size="small" />
              )}
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Progress
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
              <LinearProgress
                variant="determinate"
                value={session.progress_percentage || 0}
                sx={{ flex: 1, mr: 1 }}
              />
              <Typography variant="body2">
                {session.progress_percentage || 0}%
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Users Found
            </Typography>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {session.users_found || 0}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Typography variant="body2" color="text.secondary">
              Created
            </Typography>
            <Typography variant="body2">
              {formatDate(session.created_at)}
            </Typography>
          </Grid>
        </Grid>

        <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
          {session.status === 'running' ? (
            <Button
              size="small"
              variant="outlined"
              color="warning"
              startIcon={<StopIcon />}
              onClick={() => onStop(session)}
              sx={{ textTransform: 'none' }}
            >
              Stop
            </Button>
          ) : (
            <Button
              size="small"
              variant="outlined"
              startIcon={<PlayIcon />}
              onClick={() => onStart(session)}
              sx={{ textTransform: 'none' }}
            >
              Start
            </Button>
          )}
          <Button
            size="small"
            variant="text"
            startIcon={<VisibilityIcon />}
            onClick={() => onView(session)}
            sx={{ textTransform: 'none' }}
          >
            View
          </Button>
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { onView(session); handleMenuClose(); }}>
            <ListItemIcon>
              <VisibilityIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>View Results</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onExport(session); handleMenuClose(); }}>
            <ListItemIcon>
              <DownloadIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Export Data</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onDelete(session); handleMenuClose(); }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete Session</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
}

function CreateSessionDialog({ open, onClose, onSubmit, profiles = [] }) {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    name: '',
    post_url: '',
    scraping_types: [],
    profile_id: '',
    max_users: 1000,
    include_comments: true,
    include_replies: false,
    delay_between_requests: 2,
  });

  const { data: profilesData } = useQuery('profiles', profilesAPI.getAll);
  const profilesFromAPI = Array.isArray(profilesData?.profiles)
    ? profilesData.profiles
    : Array.isArray(profilesData?.data)
    ? profilesData.data
    : Array.isArray(profilesData)
    ? profilesData
    : [];

  // Use profiles from props if available, otherwise use fetched profiles
  const finalProfiles = Array.isArray(profiles) && profiles.length > 0 ? profiles : profilesFromAPI;

  const steps = [
    'Basic Information',
    'Scraping Configuration',
    'Advanced Settings',
  ];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSubmit = () => {
    onSubmit(formData);
    handleReset();
  };

  const handleReset = () => {
    setActiveStep(0);
    setFormData({
      name: '',
      post_url: '',
      scraping_types: [],
      profile_id: '',
      max_users: 1000,
      include_comments: true,
      include_replies: false,
      delay_between_requests: 2,
    });
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleScrapingTypeChange = (type) => {
    setFormData(prev => ({
      ...prev,
      scraping_types: prev.scraping_types.includes(type)
        ? prev.scraping_types.filter(t => t !== type)
        : [...prev.scraping_types, type]
    }));
  };

  const isStepValid = (step) => {
    switch (step) {
      case 0:
        return formData.name && formData.post_url;
      case 1:
        return formData.scraping_types.length > 0 && formData.profile_id;
      case 2:
        return true;
      default:
        return false;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Create New Scraping Session</DialogTitle>
      <DialogContent>
        <Stepper activeStep={activeStep} orientation="vertical">
          <Step>
            <StepLabel>Basic Information</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <TextField
                  fullWidth
                  label="Session Name"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />
                <TextField
                  fullWidth
                  label="Facebook Post URL"
                  value={formData.post_url}
                  onChange={(e) => handleFormChange('post_url', e.target.value)}
                  placeholder="https://facebook.com/posts/..."
                  required
                />
              </Box>
            </StepContent>
          </Step>

          <Step>
            <StepLabel>Scraping Configuration</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Select Profile
                </Typography>
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Browser Profile</InputLabel>
                  <Select
                    value={formData.profile_id}
                    onChange={(e) => handleFormChange('profile_id', e.target.value)}
                    label="Browser Profile"
                  >
                    {Array.isArray(finalProfiles) && finalProfiles.map((profile) => (
                      <MenuItem key={profile.id} value={profile.id}>
                        {profile.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <Typography variant="subtitle2" sx={{ mb: 2 }}>
                  Data to Scrape
                </Typography>
                <Grid container spacing={2}>
                  {[
                    { key: 'comments', label: 'Comments', icon: <CommentIcon /> },
                    { key: 'likes', label: 'Likes', icon: <ThumbUpIcon /> },
                    { key: 'shares', label: 'Shares', icon: <ShareIcon /> },
                  ].map((type) => (
                    <Grid item xs={4} key={type.key}>
                      <Card
                        sx={{
                          cursor: 'pointer',
                          border: formData.scraping_types.includes(type.key) ? 2 : 1,
                          borderColor: formData.scraping_types.includes(type.key)
                            ? 'primary.main'
                            : 'divider',
                        }}
                        onClick={() => handleScrapingTypeChange(type.key)}
                      >
                        <CardContent sx={{ textAlign: 'center', py: 2 }}>
                          {type.icon}
                          <Typography variant="body2" sx={{ mt: 1 }}>
                            {type.label}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </Box>
            </StepContent>
          </Step>

          <Step>
            <StepLabel>Advanced Settings</StepLabel>
            <StepContent>
              <Box sx={{ mt: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Max Users to Scrape"
                      type="number"
                      value={formData.max_users}
                      onChange={(e) => handleFormChange('max_users', parseInt(e.target.value))}
                      inputProps={{ min: 1, max: 10000 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Delay Between Requests (seconds)"
                      type="number"
                      value={formData.delay_between_requests}
                      onChange={(e) => handleFormChange('delay_between_requests', parseFloat(e.target.value))}
                      inputProps={{ min: 0.5, max: 10, step: 0.5 }}
                    />
                  </Grid>
                </Grid>
              </Box>
            </StepContent>
          </Step>
        </Stepper>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Box>
            {activeStep === steps.length - 1 ? (
              <Button
                variant="contained"
                onClick={handleSubmit}
                disabled={!isStepValid(activeStep)}
              >
                Create Session
              </Button>
            ) : (
              <Button
                variant="contained"
                onClick={handleNext}
                disabled={!isStepValid(activeStep)}
              >
                Next
              </Button>
            )}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={() => { onClose(); handleReset(); }}>
          Cancel
        </Button>
      </DialogActions>
    </Dialog>
  );
}

function Scraping() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [selectedSession, setSelectedSession] = useState(null);
  const [tabValue, setTabValue] = useState(0);

  const queryClient = useQueryClient();
  const { setScrapingSessions } = useApp();

  // Fetch profiles for dialogs
  const { data: profilesData } = useQuery('profiles', profilesAPI.getAll);
  const profiles = Array.isArray(profilesData?.profiles)
    ? profilesData.profiles
    : Array.isArray(profilesData?.data)
    ? profilesData.data
    : Array.isArray(profilesData)
    ? profilesData
    : [];

  // Fetch scraping sessions
  const { data: sessionsData, isLoading, error } = useQuery(
    'scraping-sessions',
    scrapingAPI.getSessions,
    {
      refetchInterval: 2000, // Refresh every 2 seconds for real-time updates
      onSuccess: (data) => {
        setScrapingSessions(data.data || []);

        // Log session updates for debugging
        const sessions = Array.isArray(data?.sessions) ? data.sessions :
                        Array.isArray(data?.data) ? data.data :
                        Array.isArray(data) ? data : [];

        sessions.forEach(session => {
          if (session.status === 'running') {
            console.log(`🔄 Session "${session.name}" - ${session.current_step || 'Running'} (${session.progress_percentage || 0}%)`);
          } else if (session.status === 'completed') {
            console.log(`✅ Session "${session.name}" completed - Found ${session.users_found || session.total_found || 0} users`);
          } else if (session.status === 'failed') {
            console.log(`❌ Session "${session.name}" failed - ${session.error_message || 'Unknown error'}`);
          }
        });
      },
      onError: (error) => {
        console.error('❌ Failed to load scraping sessions:', error);
        toast.error('Failed to load scraping sessions');
      },
    }
  );

  // Create session mutation
  const createMutation = useMutation(scrapingAPI.createSession, {
    onSuccess: () => {
      queryClient.invalidateQueries('scraping-sessions');
      setCreateDialogOpen(false);
      toast.success('Scraping session created successfully');
    },
    onError: (error) => {
      toast.error('Failed to create scraping session');
    },
  });

  // Start scraping mutation
  const startMutation = useMutation(
    (sessionId) => scrapingAPI.startScraping(sessionId),
    {
      onSuccess: (response, sessionId) => {
        queryClient.invalidateQueries('scraping-sessions');
        console.log(`🚀 Started scraping for session ${sessionId}:`, response);
        toast.success('Scraping started successfully');
      },
      onError: (error, sessionId) => {
        console.error(`❌ Failed to start scraping for session ${sessionId}:`, error);
        toast.error(`Failed to start scraping: ${error.response?.data?.detail || error.message}`);
      },
    }
  );

  // Stop scraping mutation
  const stopMutation = useMutation(
    (sessionId) => scrapingAPI.stopScraping(sessionId),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('scraping-sessions');
        toast.success('Scraping stopped successfully');
      },
      onError: (error) => {
        toast.error('Failed to stop scraping');
      },
    }
  );

  // Delete session mutation
  const deleteMutation = useMutation(scrapingAPI.deleteSession, {
    onSuccess: () => {
      queryClient.invalidateQueries('scraping-sessions');
      toast.success('Session deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete session');
    },
  });

  // Export mutation
  const exportMutation = useMutation(
    ({ sessionId, format }) => scrapingAPI.exportResults(sessionId, format),
    {
      onSuccess: (response, variables) => {
        // Handle file download
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `scraping_results_${variables.sessionId}.${variables.format}`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        toast.success('Export completed successfully');
      },
      onError: (error) => {
        toast.error('Failed to export data');
      },
    }
  );

  const sessions = Array.isArray(sessionsData?.sessions)
    ? sessionsData.sessions
    : Array.isArray(sessionsData?.data)
    ? sessionsData.data
    : Array.isArray(sessionsData)
    ? sessionsData
    : [];

  const handleCreate = (formData) => {
    createMutation.mutate(formData);
  };

  const handleStart = (session) => {
    console.log(`🎯 Starting scraping for session:`, {
      id: session.id,
      name: session.name,
      post_url: session.post_url,
      scraping_types: session.scraping_types,
      status: session.status
    });
    startMutation.mutate(session.id);
  };

  const handleStop = (session) => {
    stopMutation.mutate(session.id);
  };

  const handleView = (session) => {
    setSelectedSession(session);
    setViewDialogOpen(true);
  };

  const handleDelete = (session) => {
    if (window.confirm(`Are you sure you want to delete session "${session.name}"?`)) {
      deleteMutation.mutate(session.id);
    }
  };

  const handleExport = (session) => {
    exportMutation.mutate({ sessionId: session.id, format: 'xlsx' });
  };

  const activeSessions = sessions.filter(s => s.status === 'running');
  const completedSessions = sessions.filter(s => s.status === 'completed');
  const allSessions = sessions;

  if (error) {
    return (
      <Box className="fade-in">
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load scraping sessions. Please check your connection to the backend.
        </Alert>
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Facebook Scraping
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
          sx={{ textTransform: 'none' }}
        >
          New Session
        </Button>
      </Box>

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}

      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label={`All Sessions (${allSessions.length})`} />
          <Tab label={`Active (${activeSessions.length})`} />
          <Tab label={`Completed (${completedSessions.length})`} />
        </Tabs>
      </Box>

      <TabPanel value={tabValue} index={0}>
        {allSessions.length === 0 && !isLoading ? (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 8 }}>
              <SearchIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                No Scraping Sessions Found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Create your first scraping session to extract Facebook data
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setCreateDialogOpen(true)}
                sx={{ textTransform: 'none' }}
              >
                Create Session
              </Button>
            </CardContent>
          </Card>
        ) : (
          <Grid container spacing={3}>
            {allSessions.map((session) => (
              <Grid item xs={12} sm={6} md={4} key={session.id}>
                <ScrapingSessionCard
                  session={session}
                  onStart={handleStart}
                  onStop={handleStop}
                  onView={handleView}
                  onDelete={handleDelete}
                  onExport={handleExport}
                />
              </Grid>
            ))}
          </Grid>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Grid container spacing={3}>
          {activeSessions.map((session) => (
            <Grid item xs={12} sm={6} md={4} key={session.id}>
              <ScrapingSessionCard
                session={session}
                onStart={handleStart}
                onStop={handleStop}
                onView={handleView}
                onDelete={handleDelete}
                onExport={handleExport}
              />
            </Grid>
          ))}
        </Grid>
        {activeSessions.length === 0 && (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No active scraping sessions
              </Typography>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          {completedSessions.map((session) => (
            <Grid item xs={12} sm={6} md={4} key={session.id}>
              <ScrapingSessionCard
                session={session}
                onStart={handleStart}
                onStop={handleStop}
                onView={handleView}
                onDelete={handleDelete}
                onExport={handleExport}
              />
            </Grid>
          ))}
        </Grid>
        {completedSessions.length === 0 && (
          <Card>
            <CardContent sx={{ textAlign: 'center', py: 4 }}>
              <Typography variant="body1" color="text.secondary">
                No completed scraping sessions
              </Typography>
            </CardContent>
          </Card>
        )}
      </TabPanel>

      {/* Create Session Dialog */}
      <CreateSessionDialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        onSubmit={handleCreate}
        profiles={profiles}
      />

      {/* View Results Dialog */}
      <Dialog
        open={viewDialogOpen}
        onClose={() => setViewDialogOpen(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Scraping Results: {selectedSession?.name}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Results preview will be implemented here
          </Typography>
          <Alert severity="info">
            Data preview and detailed results view coming soon
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
          {selectedSession && (
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={() => handleExport(selectedSession)}
            >
              Export
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Scraping;

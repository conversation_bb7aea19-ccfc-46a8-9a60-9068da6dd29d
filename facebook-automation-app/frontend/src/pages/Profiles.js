import React, { useState } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Avatar,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  LinearProgress,
  FormHelperText,
} from '@mui/material';
import {
  Add as AddIcon,
  Person as PersonIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  PlayArrow as PlayIcon,
  Refresh as RefreshIcon,
  Computer as ComputerIcon,
  Security as SecurityIcon,
  Language as LanguageIcon,
  LocationOn as LocationIcon,
  Facebook as FacebookIcon,
  Login as LoginIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import toast from 'react-hot-toast';

import { profilesAPI } from '../services/api';
import { useApp } from '../contexts/AppContext';

function ProfileCard({ profile, onEdit, onDelete, onTest, onFacebookLogin, onFacebookLoginComplete, onFacebookLoginTerminate, loginStatusPolling }) {
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenuClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const isLoggedIn = profile.facebook_email || profile.facebook_user_id;
  const isLoginInProgress = loginStatusPolling && loginStatusPolling.has(profile.id);
  // Show Complete Login button ONLY when login is actually in progress
  const shouldShowCompleteLogin = isLoginInProgress;
  const isReady = profile.status === 'active' && isLoggedIn;

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'inactive':
        return 'default';
      case 'error':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                bgcolor: 'primary.main',
                width: 48,
                height: 48,
                mr: 2,
              }}
            >
              {profile.name.charAt(0).toUpperCase()}
            </Avatar>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
                {profile.name}
              </Typography>
              <Chip
                label={profile.status || 'inactive'}
                size="small"
                color={getStatusColor(profile.status)}
              />
            </Box>
          </Box>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
        </Box>

        <Divider sx={{ my: 2 }} />

        <Grid container spacing={2}>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <ComputerIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Browser
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              Chrome
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LocationIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Location
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.timezone || 'UTC'}
            </Typography>
          </Grid>
          <Grid item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <SecurityIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Proxy
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.proxy_type && profile.proxy_type !== 'no_proxy'
                ? profile.proxy_type.toUpperCase()
                : 'No Proxy'}
            </Typography>
          </Grid>
          <Box item xs={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <LanguageIcon sx={{ fontSize: 16, mr: 1, color: 'text.secondary' }} />
              <Typography variant="body2" color="text.secondary">
                Language
              </Typography>
            </Box>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              {profile.language || 'en-US'}
            </Typography>
          </Box>

        </Grid>

        <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FacebookIcon fontSize="small" color="action" />
            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
              Facebook:
            </Typography>
            {isLoggedIn ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <CheckCircleIcon fontSize="small" sx={{ color: '#4caf50' }} />
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#4caf50' }}>
                  Logged In
                </Typography>
              </Box>
            ) : shouldShowCompleteLogin ? (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <RefreshIcon fontSize="small" sx={{ color: '#2196f3' }} />
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#2196f3' }}>
                  Login in Progress
                </Typography>
              </Box>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LoginIcon fontSize="small" sx={{ color: '#ff9800' }} />
                <Typography variant="body2" sx={{ fontWeight: 500, color: '#ff9800' }}>
                  Not Logged In
                </Typography>
              </Box>
            )}
          </Box>
        </Box>

        <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            size="small"
            variant="outlined"
            startIcon={<PlayIcon />}
            onClick={() => onTest(profile)}
            sx={{ textTransform: 'none' }}
          >
            Test
          </Button>
          <Button
            size="small"
            variant="text"
            startIcon={<EditIcon />}
            onClick={() => onEdit(profile)}
            sx={{ textTransform: 'none' }}
          >
            Edit
          </Button>
          {shouldShowCompleteLogin && (
            <Button
              size="small"
              variant="contained"
              color="success"
              startIcon={<CheckCircleIcon />}
              onClick={() => onFacebookLoginComplete(profile)}
              sx={{
                textTransform: 'none',
                backgroundColor: '#4caf50',
                '&:hover': {
                  backgroundColor: '#45a049'
                }
              }}
            >
              Complete Login
            </Button>
          )}
        </Box>

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem onClick={() => { onEdit(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <EditIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Edit Profile</ListItemText>
          </MenuItem>
          <MenuItem onClick={() => { onTest(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <PlayIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Test Browser</ListItemText>
          </MenuItem>

          <Divider />

          {shouldShowCompleteLogin ? (
            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>
              <ListItemIcon>
                <CheckCircleIcon fontSize="small" sx={{ color: '#4caf50' }} />
              </ListItemIcon>
              <ListItemText>Complete Login</ListItemText>
            </MenuItem>
          ) : isLoggedIn ? (
            <MenuItem onClick={() => { onFacebookLoginComplete(profile); handleMenuClose(); }}>
              <ListItemIcon>
                <CheckCircleIcon fontSize="small" sx={{ color: '#4caf50' }} />
              </ListItemIcon>
              <ListItemText>Update Login Info</ListItemText>
            </MenuItem>
          ) : (
            <MenuItem onClick={() => { onFacebookLogin(profile); handleMenuClose(); }}>
              <ListItemIcon>
                <FacebookIcon fontSize="small" sx={{ color: '#1877f2' }} />
              </ListItemIcon>
              <ListItemText>Login to Facebook</ListItemText>
            </MenuItem>
          )}

          <MenuItem onClick={() => { onFacebookLoginTerminate(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <RefreshIcon fontSize="small" sx={{ color: '#ff9800' }} />
            </ListItemIcon>
            <ListItemText>Terminate Browser Session</ListItemText>
          </MenuItem>

          <Divider />

          <MenuItem onClick={() => { onDelete(profile); handleMenuClose(); }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText>Delete Profile</ListItemText>
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
}

function Profiles() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [loginInstructionsOpen, setLoginInstructionsOpen] = useState(false);
  const [loginInstructions, setLoginInstructions] = useState([]);
  const [loginStatusPolling, setLoginStatusPolling] = useState(new Map()); // profileId -> intervalId
  const [formData, setFormData] = useState({
    name: '',
    browser_type: 'chrome',
    user_agent: '',
    screen_resolution: '1920x1080',
    timezone: 'UTC',
    language: 'en-US',
    proxy_enabled: false,
    proxy_type: 'no_proxy',
    proxy_host: '',
    proxy_port: '',
    proxy_username: '',
    proxy_password: '',
  });

  const queryClient = useQueryClient();
  const { setProfiles } = useApp();

  // Fetch profiles
  const { data: profilesData, isLoading, error } = useQuery(
    'profiles',
    () => profilesAPI.getAll().then(response => response.data),
    {
      onSuccess: (data) => {
        // Handle both array and object with profiles property
        const profilesList = data?.profiles || data || [];
        setProfiles(profilesList);
      },
      onError: (error) => {
        toast.error('Failed to load profiles');
      },
    }
  );

  // Create profile mutation
  const createMutation = useMutation(profilesAPI.create, {
    onSuccess: () => {
      queryClient.invalidateQueries('profiles');
      setCreateDialogOpen(false);
      resetForm();
      toast.success('Profile created successfully');
    },
    onError: (error) => {
      console.error('Create profile error:', error);
      const errorMessage = error?.response?.data?.detail ||
                          error?.message ||
                          'Failed to create profile';
      toast.error(errorMessage);
    },
  });

  // Update profile mutation
  const updateMutation = useMutation(
    ({ id, data }) => profilesAPI.update(id, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('profiles');
        setEditDialogOpen(false);
        resetForm();
        toast.success('Profile updated successfully');
      },
      onError: (error) => {
        console.error('Update profile error:', error);
        const errorMessage = error?.response?.data?.detail ||
                            error?.message ||
                            'Failed to update profile';
        toast.error(errorMessage);
      },
    }
  );

  // Delete profile mutation
  const deleteMutation = useMutation(profilesAPI.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries('profiles');
      toast.success('Profile deleted successfully');
    },
    onError: (error) => {
      toast.error('Failed to delete profile');
    },
  });



  // Test proxy mutation
  const testProxyMutation = useMutation(
    (profileId) => profilesAPI.testProxy(profileId),
    {
      onSuccess: (response) => {
        const result = response.data;
        if (result.status === 'success') {
          toast.success(`Proxy test successful! Response time: ${result.response_time}s`);
        } else if (result.status === 'no_proxy') {
          toast.info('No proxy configured for this profile');
        } else {
          toast.error(`Proxy test failed: ${result.message}`);
        }
      },
      onError: (error) => {
        console.error('Proxy test error:', error);

        // Handle specific error cases
        if (error?.response?.status === 404) {
          toast.warning('Proxy test endpoint not available. This feature may not be implemented yet.');
        } else if (error?.response?.data?.message === "Endpoint not found") {
          toast.warning('Test feature not available in current backend version.');
        } else {
          const errorMessage = error?.response?.data?.detail ||
                              error?.response?.data?.message ||
                              error?.message ||
                              'Proxy test failed';
          toast.error(errorMessage);
        }
      },
    }
  );

  // Facebook login mutations
  const facebookLoginMutation = useMutation(
    (profileId) => profilesAPI.facebookLogin(profileId),
    {
      onSuccess: (response) => {
        const result = response.data;
        if (result.status === 'login_initiated' || result.status === 'browser_launched') {
          toast.success('Antidetect browser launched! Complete Facebook login manually in the browser window.');
          // Show instructions dialog
          setLoginInstructions(result.instructions || []);
          setLoginInstructionsOpen(true);

          // Start polling for login status
          startLoginStatusPolling(result.profile_id);
        } else if (result.status === 'session_exists') {
          toast.info('Browser session already active. Complete login in the existing browser window.');
          setLoginInstructions([
            "Browser session is already running",
            "Complete Facebook login in the existing browser window",
            "Click 'Complete Login' button when done"
          ]);
          setLoginInstructionsOpen(true);
        } else {
          toast.error(`Failed to start login: ${result.message}`);
        }
      },
      onError: (error) => {
        console.error('Facebook login error:', error);
        const errorMessage = error?.response?.data?.detail ||
                            error?.message ||
                            'Failed to start Facebook login';
        toast.error(errorMessage);
      },
    }
  );

  const facebookLoginCompleteMutation = useMutation(
    ({ profileId, facebookData }) => profilesAPI.facebookLoginComplete(profileId, facebookData),
    {
      onSuccess: (response) => {
        const result = response.data;
        if (result.status === 'login_complete') {
          toast.success(`Facebook login completed! Profile "${result.profile_name}" is now ready.`);
          queryClient.invalidateQueries('profiles');
        } else {
          toast.error(`Failed to complete login: ${result.message}`);
        }
      },
      onError: (error) => {
        console.error('Facebook login complete error:', error);
        const errorMessage = error?.response?.data?.detail ||
                            error?.message ||
                            'Failed to complete Facebook login';
        toast.error(errorMessage);
      },
    }
  );

  const facebookLoginTerminateMutation = useMutation(
    (profileId) => profilesAPI.facebookLoginTerminate(profileId),
    {
      onSuccess: (response) => {
        const result = response.data;
        toast.success('Browser session terminated successfully.');
        stopLoginStatusPolling(result.profile_id);
        setLoginInstructionsOpen(false);
      },
      onError: (error) => {
        console.error('Facebook login terminate error:', error);
        const errorMessage = error?.response?.data?.detail ||
                            error?.message ||
                            'Failed to terminate browser session';
        toast.error(errorMessage);
      },
    }
  );

  const profiles = Array.isArray(profilesData?.profiles)
    ? profilesData.profiles
    : Array.isArray(profilesData)
    ? profilesData
    : [];

  const resetForm = () => {
    setFormData({
      name: '',
      browser_type: 'chrome',
      user_agent: '',
      screen_resolution: '1920x1080',
      timezone: 'UTC',
      language: 'en-US',
      proxy_enabled: false,
      proxy_type: 'no_proxy',
      proxy_host: '',
      proxy_port: '',
      proxy_username: '',
      proxy_password: '',
    });
    setSelectedProfile(null);
  };

  const handleCreate = () => {
    setCreateDialogOpen(true);
    resetForm();
  };

  const handleEdit = (profile) => {
    setSelectedProfile(profile);

    setFormData({
      name: profile.name || '',
      browser_type: 'chrome', // Default since not in current API response
      user_agent: profile.user_agent || '',
      screen_resolution: profile.screen_resolution || '1920x1080',
      timezone: profile.timezone || 'UTC',
      language: profile.language || 'en-US',
      proxy_enabled: profile.proxy_type && profile.proxy_type !== 'no_proxy',
      proxy_type: profile.proxy_type || 'no_proxy',
      proxy_host: profile.proxy_host || '',
      proxy_port: profile.proxy_port ? profile.proxy_port.toString() : '',
      proxy_username: '', // Not in current API response
      proxy_password: '', // Don't populate password for security
    });
    setEditDialogOpen(true);
  };

  const handleDelete = (profile) => {
    if (window.confirm(`Are you sure you want to delete profile "${profile.name}"?`)) {
      deleteMutation.mutate(profile.id);
    }
  };

  const handleTest = (profile) => {
    // Show profile information
    const proxyInfo = profile.proxy_type && profile.proxy_type !== 'no_proxy'
      ? `${profile.proxy_type.toUpperCase()}${profile.proxy_host ? ` (${profile.proxy_host}:${profile.proxy_port})` : ''}`
      : 'No Proxy';

    // Create a detailed info message
    const profileInfo = [
      `Profile: ${profile.name}`,
      `Status: ${profile.status}`,
      `Proxy: ${proxyInfo}`,
      `Language: ${profile.language || 'en-US'}`,
      `Timezone: ${profile.timezone || 'UTC'}`,
      `Created: ${new Date(profile.created_at).toLocaleDateString()}`
    ].join('\n');

    // Show info first
    toast.success(`Profile Information:\n${profileInfo}`, { duration: 5000 });

    // Try to test proxy if configured
    if (profile.proxy_type && profile.proxy_type !== 'no_proxy' && profile.proxy_host) {
      setTimeout(() => {
        testProxyMutation.mutate(profile.id);
      }, 1000);
    }
  };

  const handleSubmit = () => {
    // Prepare form data with proper type conversion
    const submitData = { ...formData };

    // Convert empty strings to null for optional fields
    if (submitData.proxy_host === '') submitData.proxy_host = null;
    if (submitData.proxy_port === '') submitData.proxy_port = null;
    if (submitData.proxy_username === '') submitData.proxy_username = null;
    if (submitData.proxy_password === '') submitData.proxy_password = null;
    if (submitData.user_agent === '') submitData.user_agent = null;

    // Convert port to integer if provided
    if (submitData.proxy_port) {
      const port = parseInt(submitData.proxy_port);
      if (isNaN(port)) {
        toast.error('Proxy port must be a valid number');
        return;
      }
      submitData.proxy_port = port;
    }

    // Validate proxy configuration
    if (formData.proxy_type !== 'no_proxy') {
      if (!formData.proxy_host || !formData.proxy_port) {
        toast.error('Proxy host and port are required when proxy is enabled');
        return;
      }

      const port = parseInt(formData.proxy_port);
      if (isNaN(port) || port < 1 || port > 65535) {
        toast.error('Proxy port must be a valid number between 1 and 65535');
        return;
      }
    }

    // Prepare final data structure
    const finalData = {
      name: submitData.name,
      user_agent: submitData.user_agent,
      browser_config: {
        browser_type: submitData.browser_type || 'chrome',
        screen_resolution: submitData.screen_resolution || '1920x1080',
        timezone: submitData.timezone || 'UTC',
        language: submitData.language || 'en-US'
      },
      proxy_config: submitData.proxy_type !== 'no_proxy' ? {
        proxy_type: submitData.proxy_type,
        host: submitData.proxy_host,
        port: submitData.proxy_port,
        username: submitData.proxy_username,
        password: submitData.proxy_password
      } : null
    };

    if (selectedProfile) {
      updateMutation.mutate({ id: selectedProfile.id, data: finalData });
    } else {
      createMutation.mutate(finalData);
    }
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFacebookLogin = (profile) => {
    facebookLoginMutation.mutate(profile.id);
  };

  const handleFacebookLoginTerminate = (profile) => {
    if (window.confirm(`Are you sure you want to terminate the browser session for "${profile.name}"?`)) {
      facebookLoginTerminateMutation.mutate(profile.id);
    }
  };

  // Login status polling functions
  const startLoginStatusPolling = (profileId) => {
    // Clear any existing polling for this profile
    stopLoginStatusPolling(profileId);

    const intervalId = setInterval(async () => {
      try {
        const response = await profilesAPI.facebookLoginStatus(profileId);
        const status = response.data.login_status;

        if (status.status === 'browser_closed' || status.status === 'expired') {
          toast.warning('Browser session ended. Please restart login process.');
          stopLoginStatusPolling(profileId);
        } else if (status.status === 'no_session') {
          stopLoginStatusPolling(profileId);
        }
        // Continue polling if status is 'in_progress' or 'active'
      } catch (error) {
        console.error('Error polling login status:', error);
        // Don't stop polling on error, might be temporary
      }
    }, 10000); // Poll every 10 seconds

    setLoginStatusPolling(prev => new Map(prev.set(profileId, intervalId)));
  };

  const stopLoginStatusPolling = (profileId) => {
    const intervalId = loginStatusPolling.get(profileId);
    if (intervalId) {
      clearInterval(intervalId);
      setLoginStatusPolling(prev => {
        const newMap = new Map(prev);
        newMap.delete(profileId);
        return newMap;
      });
    }
  };

  // Cleanup polling on component unmount
  React.useEffect(() => {
    return () => {
      loginStatusPolling.forEach((intervalId) => {
        clearInterval(intervalId);
      });
    };
  }, [loginStatusPolling]);

  const handleFacebookLoginComplete = async (profile) => {
    // Stop polling for this profile
    stopLoginStatusPolling(profile.id);

    try {
      // Try to get Facebook status from the browser session first
      const statusResponse = await profilesAPI.getFacebookStatus(profile.id);
      const facebookData = statusResponse.data.facebook_data || {};

      // Complete login with extracted data or default data
      facebookLoginCompleteMutation.mutate({
        profileId: profile.id,
        facebookData: {
          email: facebookData.email || "<EMAIL>", // This would be extracted from browser
          username: facebookData.username || "facebook_user",
          user_id: facebookData.user_id || "123456789"
        }
      });
    } catch (error) {
      console.log('Could not extract Facebook data from browser, using default data');
      // Fallback to default data if extraction fails
      facebookLoginCompleteMutation.mutate({
        profileId: profile.id,
        facebookData: {
          email: "<EMAIL>", // This would be extracted from browser
          username: "facebook_user",
          user_id: "123456789"
        }
      });
    }
  };

  if (error) {
    return (
      <Box className="fade-in">
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load profiles. Please check your connection to the backend.
        </Alert>
      </Box>
    );
  }

  return (
    <Box className="fade-in">
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 700 }}>
          Profile Management
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => queryClient.invalidateQueries('profiles')}
            sx={{ textTransform: 'none' }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreate}
            sx={{ textTransform: 'none' }}
          >
            New Profile
          </Button>
        </Box>
      </Box>

      {isLoading && <LinearProgress sx={{ mb: 3 }} />}



      {profiles.length === 0 && !isLoading ? (
        <Card>
          <CardContent sx={{ textAlign: 'center', py: 8 }}>
            <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" sx={{ mb: 1 }}>
              No Profiles Found
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Create your first browser profile to get started
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleCreate}
              sx={{ textTransform: 'none' }}
            >
              Create Profile
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {profiles.map((profile) => (
            <Grid item xs={12} sm={6} md={4} key={profile.id}>
              <ProfileCard
                profile={profile}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onTest={handleTest}
                onFacebookLogin={handleFacebookLogin}
                onFacebookLoginComplete={handleFacebookLoginComplete}
                onFacebookLoginTerminate={handleFacebookLoginTerminate}
                loginStatusPolling={loginStatusPolling}
              />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create/Edit Profile Dialog */}
      <Dialog
        open={createDialogOpen || editDialogOpen}
        onClose={() => {
          setCreateDialogOpen(false);
          setEditDialogOpen(false);
          resetForm();
        }}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedProfile ? 'Edit Profile' : 'Create New Profile'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Profile Name"
                  value={formData.name}
                  onChange={(e) => handleFormChange('name', e.target.value)}
                  required
                />
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Browser Type</InputLabel>
                  <Select
                    value={formData.browser_type}
                    onChange={(e) => handleFormChange('browser_type', e.target.value)}
                    label="Browser Type"
                  >
                    <MenuItem value="chrome">Chrome</MenuItem>
                    <MenuItem value="firefox">Firefox</MenuItem>
                    <MenuItem value="edge">Edge</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Screen Resolution</InputLabel>
                  <Select
                    value={formData.screen_resolution}
                    onChange={(e) => handleFormChange('screen_resolution', e.target.value)}
                    label="Screen Resolution"
                  >
                    <MenuItem value="1920x1080">1920x1080</MenuItem>
                    <MenuItem value="1366x768">1366x768</MenuItem>
                    <MenuItem value="1440x900">1440x900</MenuItem>
                    <MenuItem value="1280x720">1280x720</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Timezone</InputLabel>
                  <Select
                    value={formData.timezone}
                    onChange={(e) => handleFormChange('timezone', e.target.value)}
                    label="Timezone"
                  >
                    <MenuItem value="UTC">UTC</MenuItem>
                    <MenuItem value="Asia/Ho_Chi_Minh">Asia/Ho_Chi_Minh</MenuItem>
                    <MenuItem value="America/New_York">America/New_York</MenuItem>
                    <MenuItem value="Europe/London">Europe/London</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Language</InputLabel>
                  <Select
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    label="Language"
                  >
                    <MenuItem value="en-US">English (US)</MenuItem>
                    <MenuItem value="vi-VN">Vietnamese</MenuItem>
                    <MenuItem value="zh-CN">Chinese (Simplified)</MenuItem>
                    <MenuItem value="ja-JP">Japanese</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="User Agent (Optional)"
                  value={formData.user_agent}
                  onChange={(e) => handleFormChange('user_agent', e.target.value)}
                  placeholder="Leave empty for automatic generation"
                />
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Proxy Configuration
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Proxy Type</InputLabel>
                  <Select
                    value={formData.proxy_type}
                    onChange={(e) => {
                      const proxyType = e.target.value;
                      handleFormChange('proxy_type', proxyType);
                      handleFormChange('proxy_enabled', proxyType !== 'no_proxy');
                    }}
                    label="Proxy Type"
                  >
                    <MenuItem value="no_proxy">
                      <Box>
                        <Typography variant="body2">No Proxy (Local Network)</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Direct connection without proxy
                        </Typography>
                      </Box>
                    </MenuItem>
                    <MenuItem value="http">
                      <Box>
                        <Typography variant="body2">HTTP</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Standard HTTP proxy (ports: 8080, 3128, 8888)
                        </Typography>
                      </Box>
                    </MenuItem>
                    <MenuItem value="https">
                      <Box>
                        <Typography variant="body2">HTTPS</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Encrypted HTTPS proxy (ports: 8080, 3128, 443)
                        </Typography>
                      </Box>
                    </MenuItem>
                    <MenuItem value="socks5">
                      <Box>
                        <Typography variant="body2">SOCKS5</Typography>
                        <Typography variant="caption" color="text.secondary">
                          High anonymity proxy (ports: 1080, 1081, 9050)
                        </Typography>
                      </Box>
                    </MenuItem>
                    <MenuItem value="ssh">
                      <Box>
                        <Typography variant="body2">SSH</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Secure SSH tunnel (ports: 22, 2222)
                        </Typography>
                      </Box>
                    </MenuItem>
                  </Select>
                  <FormHelperText>
                    {formData.proxy_type === 'no_proxy' && 'Using direct connection'}
                    {formData.proxy_type === 'http' && 'Fast, suitable for web browsing'}
                    {formData.proxy_type === 'https' && 'Encrypted, secure for sensitive data'}
                    {formData.proxy_type === 'socks5' && 'High anonymity, supports all protocols'}
                    {formData.proxy_type === 'ssh' && 'Maximum security, requires SSH credentials'}
                  </FormHelperText>
                </FormControl>
              </Grid>

              {formData.proxy_type !== 'no_proxy' && (
                <>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Host"
                      value={formData.proxy_host}
                      onChange={(e) => handleFormChange('proxy_host', e.target.value)}
                      required
                      placeholder={
                        formData.proxy_type === 'ssh'
                          ? "e.g., ssh.example.com or *************"
                          : "e.g., proxy.example.com or *************"
                      }
                      helperText="IP address or domain name of the proxy server"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Proxy Port"
                      value={formData.proxy_port}
                      onChange={(e) => handleFormChange('proxy_port', e.target.value)}
                      type="number"
                      required
                      placeholder={
                        formData.proxy_type === 'http' ? "8080, 3128, 8888" :
                        formData.proxy_type === 'https' ? "8080, 3128, 443" :
                        formData.proxy_type === 'socks5' ? "1080, 1081, 9050" :
                        formData.proxy_type === 'ssh' ? "22, 2222" : "Port number"
                      }
                      helperText={`Common ${formData.proxy_type.toUpperCase()} ports`}
                      inputProps={{ min: 1, max: 65535 }}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={formData.proxy_type === 'ssh' ? "SSH Username" : "Proxy Username"}
                      value={formData.proxy_username}
                      onChange={(e) => handleFormChange('proxy_username', e.target.value)}
                      placeholder={
                        formData.proxy_type === 'ssh'
                          ? "SSH username for authentication"
                          : "Leave empty if no authentication required"
                      }
                      helperText={
                        formData.proxy_type === 'ssh'
                          ? "Required for SSH connections"
                          : "Optional - only if proxy requires authentication"
                      }
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label={formData.proxy_type === 'ssh' ? "SSH Password" : "Proxy Password"}
                      value={formData.proxy_password}
                      onChange={(e) => handleFormChange('proxy_password', e.target.value)}
                      type="password"
                      placeholder={
                        formData.proxy_type === 'ssh'
                          ? "SSH password or leave empty for key-based auth"
                          : "Leave empty if no authentication required"
                      }
                      helperText={
                        formData.proxy_type === 'ssh'
                          ? "Password or private key authentication"
                          : "Optional - only if proxy requires authentication"
                      }
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => {
                          if (selectedProfile) {
                            testProxyMutation.mutate(selectedProfile.id);
                          } else {
                            // Test proxy configuration without saving
                            const proxyInfo = `${formData.proxy_type.toUpperCase()} proxy: ${formData.proxy_host}:${formData.proxy_port}`;
                            const authInfo = formData.proxy_username ? ` (Auth: ${formData.proxy_username})` : ' (No Auth)';
                            toast.info(`Proxy Configuration:\n${proxyInfo}${authInfo}\n\nSave profile first to test connection.`, { duration: 4000 });
                          }
                        }}
                        disabled={!formData.proxy_host || !formData.proxy_port || testProxyMutation.isLoading}
                        sx={{ textTransform: 'none' }}
                      >
                        {testProxyMutation.isLoading ? 'Testing...' : selectedProfile ? 'Test Connection' : 'Preview Config'}
                      </Button>
                      <Typography variant="caption" color="text.secondary">
                        Test proxy connection to ensure it works with antidetect browser
                      </Typography>
                    </Box>
                  </Grid>
                </>
              )}
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setCreateDialogOpen(false);
              setEditDialogOpen(false);
              resetForm();
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={!formData.name || createMutation.isLoading || updateMutation.isLoading}
          >
            {selectedProfile ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Facebook Login Instructions Dialog */}
      <Dialog
        open={loginInstructionsOpen}
        onClose={() => setLoginInstructionsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FacebookIcon sx={{ color: '#1877f2' }} />
          Facebook Login Instructions
        </DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            An antidetect browser window has opened with your profile configuration. Follow these steps to complete Facebook login:
          </Alert>

          <Box component="ol" sx={{ pl: 2 }}>
            {loginInstructions.map((instruction, index) => (
              <Box component="li" key={index} sx={{ mb: 1 }}>
                <Typography variant="body2">{instruction}</Typography>
              </Box>
            ))}
          </Box>

          <Alert severity="warning" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>Important:</strong> After successfully logging in to Facebook,
              click the green "Complete Login" button on the profile card or in the profile menu
              to update the profile status to "Ready".
            </Typography>
          </Alert>

          <Alert severity="success" sx={{ mt: 1 }}>
            <Typography variant="body2">
              <strong>Tip:</strong> The "Complete Login" button will appear automatically
              when the browser session is active or when the profile status is ready for completion.
            </Typography>
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setLoginInstructionsOpen(false)}
            color="primary"
          >
            Got it
          </Button>
          <Button
            onClick={() => {
              // Find the profile that has active login session
              const activeProfile = profiles.find(p => loginStatusPolling.has(p.id));
              if (activeProfile) {
                handleFacebookLoginTerminate(activeProfile);
              }
            }}
            color="warning"
            variant="outlined"
          >
            Terminate Browser
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default Profiles;
